<?xml version="1.0" encoding="utf-8"?>
<unattend xmlns="urn:schemas-microsoft-com:unattend" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State">
  <!-- UnattendedWinstall https://github.com/memstechtips/UnattendedWinstall -->
  <settings pass="offlineServicing"></settings>
  <settings pass="windowsPE">
    <component name="Microsoft-Windows-Setup" processorArchitecture="x86" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS">
      <UserData>
        <ProductKey>
          <Key>00000-00000-00000-00000-00000</Key>
        </ProductKey>
        <AcceptEula>true</AcceptEula>
      </UserData>
      <RunSynchronous>
        <RunSynchronousCommand wcm:action="add">
          <Order>1</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassTPMCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>2</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassSecureBootCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>3</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassStorageCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>4</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassCPUCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>5</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassRAMCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>6</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassDiskCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <!--Disables Windows Defender from Starting -->
        <RunSynchronousCommand wcm:action="add">
          <Order>7</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo WScript.Echo "Scanning for newly created SYSTEM registry hive file to disable Windows Defender services...""</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>8</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Set fso = CreateObject( "Scripting.FileSystemObject" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>9</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Set dic = CreateObject( "Scripting.Dictionary" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>10</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo initialized = false"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>11</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Function Execute( command )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>12</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     WScript.Echo "Running command '" + command + "'""</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>13</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Set shell = CreateObject( "WScript.Shell" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>14</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Set exec = shell.Exec( command )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>15</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Do While exec.Status = 0"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>16</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo          WScript.Sleep 100"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>17</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Loop"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>18</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     WScript.Echo exec.StdOut.ReadAll"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>19</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     WScript.Echo exec.StdErr.ReadAll"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>20</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Execute = exec.ExitCode"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>21</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo End Function"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>22</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Do"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>23</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     For Each drive In fso.Drives"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>24</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo         If drive.IsReady Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>25</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo             If drive.DriveLetter ^&lt;^&gt; "X" Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>26</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                 For Each folder In Array( "$Windows.~BT\NewOS\Windows", "Windows" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>27</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                     file = fso.BuildPath( fso.BuildPath( drive.RootFolder, folder ), "System32\config\SYSTEM" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>28</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                     If fso.FileExists( file ) And fso.FileExists( file + ".LOG1" ) And fso.FileExists( file + ".LOG2" ) Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>29</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                         If Not initialized Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>30</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             dic.Add file, Nothing"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>31</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                         ElseIf Not dic.Exists( file ) Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>32</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Set shell = CreateObject( "WScript.Shell" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>33</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             ret = 1"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>34</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Do"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>35</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                                 WScript.Sleep 500"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>36</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                                 ret = Execute( "reg.exe LOAD HKLM\mount " + file )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>37</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Loop While ret ^&gt; 0"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>38</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             For Each service In Array( "Sense", "WdBoot", "WdFilter", "WdNisDrv", "WdNisSvc", "WinDefend" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>39</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                                 ret = Execute( "reg.exe ADD HKLM\mount\ControlSet001\Services\" + service + " /v Start /t REG_DWORD /d 4 /f" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>40</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Next"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>41</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             ret = Execute( "reg.exe UNLOAD HKLM\mount" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>42</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             WScript.Echo "Found SYSTEM registry hive file at '" + file + "'. This window will now close.""</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>43</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             WScript.Sleep 5000"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>44</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Exit Do"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>45</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                         End If"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>46</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                     End If"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>47</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                 Next"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>48</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo             End If"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>49</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo         End If"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>50</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Next"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>51</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     initialized = true"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>52</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     WScript.Sleep 1000"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>53</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Loop"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>54</Order>
          <Path>cmd.exe /c "start /MIN cscript.exe //E:vbscript X:\disable-defender.vbs"</Path>
        </RunSynchronousCommand>
        <!--Prevents auto detection of Windows Edition and forces Windows Setup to show all available Editions of Windows during setup.-->
        <RunSynchronousCommand wcm:action="add">
          <Order>55</Order>
          <Path>cmd.exe /c del /f /q X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>56</Order>
          <Path>cmd.exe /c echo [Channel] > X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>57</Order>
          <Path>cmd.exe /c echo _Default >> X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>58</Order>
          <Path>cmd.exe /c echo [VL] >> X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>59</Order>
          <Path>cmd.exe /c echo 0 >> X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
      </RunSynchronous>
    </component>
    <component name="Microsoft-Windows-Setup" processorArchitecture="arm64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS">
      <UserData>
        <ProductKey>
          <Key>00000-00000-00000-00000-00000</Key>
        </ProductKey>
        <AcceptEula>true</AcceptEula>
      </UserData>
      <RunSynchronous>
        <RunSynchronousCommand wcm:action="add">
          <Order>1</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassTPMCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>2</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassSecureBootCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>3</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassStorageCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>4</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassCPUCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>5</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassRAMCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>6</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassDiskCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <!--Disables Windows Defender from Starting -->
        <RunSynchronousCommand wcm:action="add">
          <Order>7</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo WScript.Echo "Scanning for newly created SYSTEM registry hive file to disable Windows Defender services...""</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>8</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Set fso = CreateObject( "Scripting.FileSystemObject" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>9</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Set dic = CreateObject( "Scripting.Dictionary" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>10</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo initialized = false"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>11</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Function Execute( command )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>12</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     WScript.Echo "Running command '" + command + "'""</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>13</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Set shell = CreateObject( "WScript.Shell" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>14</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Set exec = shell.Exec( command )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>15</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Do While exec.Status = 0"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>16</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo          WScript.Sleep 100"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>17</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Loop"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>18</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     WScript.Echo exec.StdOut.ReadAll"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>19</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     WScript.Echo exec.StdErr.ReadAll"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>20</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Execute = exec.ExitCode"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>21</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo End Function"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>22</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Do"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>23</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     For Each drive In fso.Drives"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>24</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo         If drive.IsReady Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>25</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo             If drive.DriveLetter ^&lt;^&gt; "X" Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>26</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                 For Each folder In Array( "$Windows.~BT\NewOS\Windows", "Windows" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>27</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                     file = fso.BuildPath( fso.BuildPath( drive.RootFolder, folder ), "System32\config\SYSTEM" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>28</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                     If fso.FileExists( file ) And fso.FileExists( file + ".LOG1" ) And fso.FileExists( file + ".LOG2" ) Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>29</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                         If Not initialized Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>30</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             dic.Add file, Nothing"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>31</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                         ElseIf Not dic.Exists( file ) Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>32</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Set shell = CreateObject( "WScript.Shell" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>33</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             ret = 1"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>34</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Do"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>35</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                                 WScript.Sleep 500"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>36</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                                 ret = Execute( "reg.exe LOAD HKLM\mount " + file )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>37</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Loop While ret ^&gt; 0"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>38</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             For Each service In Array( "Sense", "WdBoot", "WdFilter", "WdNisDrv", "WdNisSvc", "WinDefend" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>39</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                                 ret = Execute( "reg.exe ADD HKLM\mount\ControlSet001\Services\" + service + " /v Start /t REG_DWORD /d 4 /f" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>40</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Next"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>41</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             ret = Execute( "reg.exe UNLOAD HKLM\mount" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>42</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             WScript.Echo "Found SYSTEM registry hive file at '" + file + "'. This window will now close.""</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>43</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             WScript.Sleep 5000"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>44</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Exit Do"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>45</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                         End If"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>46</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                     End If"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>47</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                 Next"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>48</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo             End If"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>49</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo         End If"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>50</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Next"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>51</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     initialized = true"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>52</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     WScript.Sleep 1000"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>53</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Loop"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>54</Order>
          <Path>cmd.exe /c "start /MIN cscript.exe //E:vbscript X:\disable-defender.vbs"</Path>
        </RunSynchronousCommand>
        <!--Prevents auto detection of Windows Edition and forces Windows Setup to show all available Editions of Windows during setup.-->
        <RunSynchronousCommand wcm:action="add">
          <Order>55</Order>
          <Path>cmd.exe /c del /f /q X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>56</Order>
          <Path>cmd.exe /c echo [Channel] > X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>57</Order>
          <Path>cmd.exe /c echo _Default >> X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>58</Order>
          <Path>cmd.exe /c echo [VL] >> X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>59</Order>
          <Path>cmd.exe /c echo 0 >> X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
      </RunSynchronous>
    </component>
    <component name="Microsoft-Windows-Setup" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS">
      <UserData>
        <ProductKey>
          <Key>00000-00000-00000-00000-00000</Key>
        </ProductKey>
        <AcceptEula>true</AcceptEula>
      </UserData>
      <RunSynchronous>
        <RunSynchronousCommand wcm:action="add">
          <Order>1</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassTPMCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>2</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassSecureBootCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>3</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassStorageCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>4</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassCPUCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>5</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassRAMCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>6</Order>
          <Path>reg.exe add "HKLM\SYSTEM\Setup\LabConfig" /v BypassDiskCheck /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <!--Disables Windows Defender from Starting -->
        <RunSynchronousCommand wcm:action="add">
          <Order>7</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo WScript.Echo "Scanning for newly created SYSTEM registry hive file to disable Windows Defender services...""</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>8</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Set fso = CreateObject( "Scripting.FileSystemObject" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>9</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Set dic = CreateObject( "Scripting.Dictionary" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>10</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo initialized = false"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>11</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Function Execute( command )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>12</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     WScript.Echo "Running command '" + command + "'""</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>13</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Set shell = CreateObject( "WScript.Shell" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>14</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Set exec = shell.Exec( command )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>15</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Do While exec.Status = 0"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>16</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo          WScript.Sleep 100"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>17</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Loop"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>18</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     WScript.Echo exec.StdOut.ReadAll"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>19</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     WScript.Echo exec.StdErr.ReadAll"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>20</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Execute = exec.ExitCode"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>21</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo End Function"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>22</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Do"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>23</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     For Each drive In fso.Drives"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>24</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo         If drive.IsReady Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>25</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo             If drive.DriveLetter ^&lt;^&gt; "X" Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>26</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                 For Each folder In Array( "$Windows.~BT\NewOS\Windows", "Windows" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>27</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                     file = fso.BuildPath( fso.BuildPath( drive.RootFolder, folder ), "System32\config\SYSTEM" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>28</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                     If fso.FileExists( file ) And fso.FileExists( file + ".LOG1" ) And fso.FileExists( file + ".LOG2" ) Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>29</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                         If Not initialized Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>30</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             dic.Add file, Nothing"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>31</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                         ElseIf Not dic.Exists( file ) Then"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>32</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Set shell = CreateObject( "WScript.Shell" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>33</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             ret = 1"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>34</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Do"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>35</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                                 WScript.Sleep 500"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>36</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                                 ret = Execute( "reg.exe LOAD HKLM\mount " + file )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>37</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Loop While ret ^&gt; 0"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>38</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             For Each service In Array( "Sense", "WdBoot", "WdFilter", "WdNisDrv", "WdNisSvc", "WinDefend" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>39</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                                 ret = Execute( "reg.exe ADD HKLM\mount\ControlSet001\Services\" + service + " /v Start /t REG_DWORD /d 4 /f" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>40</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Next"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>41</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             ret = Execute( "reg.exe UNLOAD HKLM\mount" )"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>42</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             WScript.Echo "Found SYSTEM registry hive file at '" + file + "'. This window will now close.""</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>43</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             WScript.Sleep 5000"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>44</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                             Exit Do"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>45</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                         End If"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>46</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                     End If"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>47</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo                 Next"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>48</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo             End If"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>49</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo         End If"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>50</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     Next"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>51</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     initialized = true"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>52</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo     WScript.Sleep 1000"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>53</Order>
          <Path>cmd.exe /c "&gt;&gt;"X:\disable-defender.vbs" echo Loop"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>54</Order>
          <Path>cmd.exe /c "start /MIN cscript.exe //E:vbscript X:\disable-defender.vbs"</Path>
        </RunSynchronousCommand>
        <!--Prevents auto detection of Windows Edition and forces Windows Setup to show all available Editions of Windows during setup.-->
        <RunSynchronousCommand wcm:action="add">
          <Order>55</Order>
          <Path>cmd.exe /c del /f /q X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>56</Order>
          <Path>cmd.exe /c echo [Channel] > X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>57</Order>
          <Path>cmd.exe /c echo _Default >> X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>58</Order>
          <Path>cmd.exe /c echo [VL] >> X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>59</Order>
          <Path>cmd.exe /c echo 0 >> X:\Sources\ei.cfg</Path>
        </RunSynchronousCommand>
      </RunSynchronous>
    </component>
  </settings>
  <settings pass="generalize"></settings>
  <settings pass="specialize">
    <component name="Microsoft-Windows-Deployment" processorArchitecture="x86" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS">
      <RunSynchronous>
        <RunSynchronousCommand wcm:action="add">
          <Order>1</Order>
          <!-- Loads Scripts in File -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -Command "$xml = [xml]::new(); $xml.Load('C:\Windows\Panther\unattend.xml'); $sb = [scriptblock]::Create( $xml.unattend.Extensions.ExtractScript ); Invoke-Command -ScriptBlock $sb -ArgumentList $xml;"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>2</Order>
          <!-- Disables User Account Control -->
          <Path>reg.exe add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA /t REG_DWORD /d 0 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>3</Order>
          <!-- Enables Running of PowerShell Scripts -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -Command "Set-ExecutionPolicy RemoteSigned -Scope LocalMachine -Force"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>4</Order>
          <!-- Skips Forced Microsoft Account Creation -->
          <Path>reg.exe add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\OOBE" /v BypassNRO /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>5</Order>
          <!-- Runs Script to Enable .NET3.5 from Windows Installation Media -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -File "C:\Windows\Temp\DotNet.ps1"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>6</Order>
          <!-- Creates Specialize Phase Marker File -->
          <Path>cmd.exe /c echo Specialized Setup > "C:\specialize_marker.txt"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>7</Order>
          <!-- Runs Recommended UnattendedWinstall Scripts -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -File "C:\Windows\Setup\Scripts\UWScript.ps1"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>8</Order>
          <!-- Creates UnattendedWinstall Script Shortcut on Desktop -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -File "C:\Windows\Temp\UWScriptShortcut.ps1"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>9</Order>
          <!-- Loads Default User Registry Hive to Make Changes to it -->
          <Path>reg.exe load "HKU\DefaultUser" "C:\Users\<USER>\NTUSER.DAT"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>10</Order>
          <!-- Add Registry Key to Run User Account Customization Script -->
          <Path>reg.exe add "HKU\DefaultUser\Software\Microsoft\Windows\CurrentVersion\Runonce" /v "CurrentUser" /t REG_SZ /d "cmd.exe /c powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -File \"C:\Users\<USER>\User Customization.ps1\"" /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>11</Order>
          <!-- Unloads Default User Registry Hive. -->
          <Path>reg.exe unload "HKU\DefaultUser"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <!-- Cleanup temporary .ps1 and .reg scripts inside C:\Windows\Temp -->
          <Order>12</Order>
          <Path>cmd /c del /f /q "C:\Windows\Temp\*.ps1" "C:\Windows\Temp\*.reg"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>13</Order>
          <!-- Deletes Specialize Phase Marker File -->
          <Path>cmd.exe /c del /q "C:\specialize_marker.txt"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <!-- Disable All Network Adapters Temporarily so Windows Doesn't Update During OOBE -->
          <Order>14</Order>
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -Command "Get-NetAdapter | Disable-NetAdapter -Confirm:$false"</Path>
        </RunSynchronousCommand>
      </RunSynchronous>
    </component>
    <component name="Microsoft-Windows-Deployment" processorArchitecture="arm64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS">
      <RunSynchronous>
        <RunSynchronousCommand wcm:action="add">
          <Order>1</Order>
          <!-- Loads Scripts in File -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -Command "$xml = [xml]::new(); $xml.Load('C:\Windows\Panther\unattend.xml'); $sb = [scriptblock]::Create( $xml.unattend.Extensions.ExtractScript ); Invoke-Command -ScriptBlock $sb -ArgumentList $xml;"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>2</Order>
          <!-- Disables User Account Control -->
          <Path>reg.exe add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA /t REG_DWORD /d 0 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>3</Order>
          <!-- Enables Running of PowerShell Scripts -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -Command "Set-ExecutionPolicy RemoteSigned -Scope LocalMachine -Force"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>4</Order>
          <!-- Skips Forced Microsoft Account Creation -->
          <Path>reg.exe add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\OOBE" /v BypassNRO /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>5</Order>
          <!-- Runs Script to Enable .NET3.5 from Windows Installation Media -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -File "C:\Windows\Temp\DotNet.ps1"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>6</Order>
          <!-- Creates Specialize Phase Marker File -->
          <Path>cmd.exe /c echo Specialized Setup > "C:\specialize_marker.txt"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>7</Order>
          <!-- Runs Recommended UnattendedWinstall Scripts -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -File "C:\Windows\Setup\Scripts\UWScript.ps1"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>8</Order>
          <!-- Creates UnattendedWinstall Script Shortcut on Desktop -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -File "C:\Windows\Temp\UWScriptShortcut.ps1"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>9</Order>
          <!-- Loads Default User Registry Hive to Make Changes to it -->
          <Path>reg.exe load "HKU\DefaultUser" "C:\Users\<USER>\NTUSER.DAT"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>10</Order>
          <!-- Add Registry Key to Run User Account Customization Script -->
          <Path>reg.exe add "HKU\DefaultUser\Software\Microsoft\Windows\CurrentVersion\Runonce" /v "CurrentUser" /t REG_SZ /d "cmd.exe /c powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -File \"C:\Users\<USER>\User Customization.ps1\"" /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>11</Order>
          <!-- Unloads Default User Registry Hive. -->
          <Path>reg.exe unload "HKU\DefaultUser"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <!-- Cleanup temporary .ps1 and .reg scripts inside C:\Windows\Temp -->
          <Order>12</Order>
          <Path>cmd /c del /f /q "C:\Windows\Temp\*.ps1" "C:\Windows\Temp\*.reg"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>13</Order>
          <!-- Deletes Specialize Phase Marker File -->
          <Path>cmd.exe /c del /q "C:\specialize_marker.txt"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <!-- Disable All Network Adapters Temporarily so Windows Doesn't Update During OOBE -->
          <Order>14</Order>
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -Command "Get-NetAdapter | Disable-NetAdapter -Confirm:$false"</Path>
        </RunSynchronousCommand>
      </RunSynchronous>
    </component>
    <component name="Microsoft-Windows-Deployment" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS">
      <RunSynchronous>
        <RunSynchronousCommand wcm:action="add">
          <Order>1</Order>
          <!-- Loads Scripts in File -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -Command "$xml = [xml]::new(); $xml.Load('C:\Windows\Panther\unattend.xml'); $sb = [scriptblock]::Create( $xml.unattend.Extensions.ExtractScript ); Invoke-Command -ScriptBlock $sb -ArgumentList $xml;"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>2</Order>
          <!-- Disables User Account Control -->
          <Path>reg.exe add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA /t REG_DWORD /d 0 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>3</Order>
          <!-- Enables Running of PowerShell Scripts -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -Command "Set-ExecutionPolicy RemoteSigned -Scope LocalMachine -Force"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>4</Order>
          <!-- Skips Forced Microsoft Account Creation -->
          <Path>reg.exe add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\OOBE" /v BypassNRO /t REG_DWORD /d 1 /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>5</Order>
          <!-- Runs Script to Enable .NET3.5 from Windows Installation Media -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -File "C:\Windows\Temp\DotNet.ps1"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>6</Order>
          <!-- Creates Specialize Phase Marker File -->
          <Path>cmd.exe /c echo Specialized Setup > "C:\specialize_marker.txt"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>7</Order>
          <!-- Runs Recommended UnattendedWinstall Scripts -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -File "C:\Windows\Setup\Scripts\UWScript.ps1"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>8</Order>
          <!-- Creates UnattendedWinstall Script Shortcut on Desktop -->
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -File "C:\Windows\Temp\UWScriptShortcut.ps1"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>9</Order>
          <!-- Loads Default User Registry Hive to Make Changes to it -->
          <Path>reg.exe load "HKU\DefaultUser" "C:\Users\<USER>\NTUSER.DAT"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>10</Order>
          <!-- Add Registry Key to Run User Account Customization Script -->
          <Path>reg.exe add "HKU\DefaultUser\Software\Microsoft\Windows\CurrentVersion\Runonce" /v "CurrentUser" /t REG_SZ /d "cmd.exe /c powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -File \"C:\Users\<USER>\User Customization.ps1\"" /f</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>11</Order>
          <!-- Unloads Default User Registry Hive. -->
          <Path>reg.exe unload "HKU\DefaultUser"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <!-- Cleanup temporary .ps1 and .reg scripts inside C:\Windows\Temp -->
          <Order>12</Order>
          <Path>cmd /c del /f /q "C:\Windows\Temp\*.ps1" "C:\Windows\Temp\*.reg"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <Order>13</Order>
          <!-- Deletes Specialize Phase Marker File -->
          <Path>cmd.exe /c del /q "C:\specialize_marker.txt"</Path>
        </RunSynchronousCommand>
        <RunSynchronousCommand wcm:action="add">
          <!-- Disable All Network Adapters Temporarily so Windows Doesn't Update During OOBE -->
          <Order>14</Order>
          <Path>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -Command "Get-NetAdapter | Disable-NetAdapter -Confirm:$false"</Path>
        </RunSynchronousCommand>
      </RunSynchronous>
    </component>
  </settings>
  <settings pass="auditSystem"></settings>
  <settings pass="auditUser"></settings>
  <settings pass="oobeSystem">
    <component name="Microsoft-Windows-Shell-Setup" processorArchitecture="x86" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS">
      <OOBE>
        <HideEULAPage>true</HideEULAPage>
        <HideOEMRegistrationScreen>true</HideOEMRegistrationScreen>
        <HideOnlineAccountScreens>true</HideOnlineAccountScreens>
        <HideWirelessSetupInOOBE>true</HideWirelessSetupInOOBE>
        <NetworkLocation>Work</NetworkLocation>
        <ProtectYourPC>3</ProtectYourPC>
      </OOBE>
      <FirstLogonCommands>
        <SynchronousCommand>
        <!-- Enables Network Adapters After OOBE Completes -->
        <Order>1</Order>
        <CommandLine>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -Command "Get-NetAdapter | Enable-NetAdapter -Confirm:$false"</CommandLine>
        </SynchronousCommand>
    </FirstLogonCommands>
    </component>
    <component name="Microsoft-Windows-Shell-Setup" processorArchitecture="arm64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS">
      <OOBE>
        <HideEULAPage>true</HideEULAPage>
        <HideOEMRegistrationScreen>true</HideOEMRegistrationScreen>
        <HideOnlineAccountScreens>true</HideOnlineAccountScreens>
        <HideWirelessSetupInOOBE>true</HideWirelessSetupInOOBE>
        <NetworkLocation>Work</NetworkLocation>
        <ProtectYourPC>3</ProtectYourPC>
      </OOBE>
      <FirstLogonCommands>
        <SynchronousCommand>
        <!-- Enables Network Adapters After OOBE Completes -->
        <Order>1</Order>
        <CommandLine>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -Command "Get-NetAdapter | Enable-NetAdapter -Confirm:$false"</CommandLine>
        </SynchronousCommand>
    </FirstLogonCommands>
    </component>
    <component name="Microsoft-Windows-Shell-Setup" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS">
      <OOBE>
        <HideEULAPage>true</HideEULAPage>
        <HideOEMRegistrationScreen>true</HideOEMRegistrationScreen>
        <HideOnlineAccountScreens>true</HideOnlineAccountScreens>
        <HideWirelessSetupInOOBE>true</HideWirelessSetupInOOBE>
        <NetworkLocation>Work</NetworkLocation>
        <ProtectYourPC>3</ProtectYourPC>
      </OOBE>
      <FirstLogonCommands>
        <SynchronousCommand>
        <!-- Enables Network Adapters After OOBE Completes -->
        <Order>1</Order>
        <CommandLine>powershell.exe -NoProfile -WindowStyle Hidden -ExecutionPolicy Bypass -Command "Get-NetAdapter | Enable-NetAdapter -Confirm:$false"</CommandLine>
        </SynchronousCommand>
    </FirstLogonCommands>
    </component>
  </settings>
  <Extensions>
    <ExtractScript>
param(
    [xml] $Document
);

$scriptsDir = 'C:\Windows\Setup\Scripts\';
foreach( $file in $Document.unattend.Extensions.File ) {
    $path = [System.Environment]::ExpandEnvironmentVariables(
        $file.GetAttribute( 'path' )
    );
    if( $path.StartsWith( $scriptsDir ) ) {
        mkdir -Path $scriptsDir -ErrorAction 'SilentlyContinue';
    }
    $encoding = switch( [System.IO.Path]::GetExtension( $path ) ) {
        { $_ -in '.ps1', '.xml' } { [System.Text.Encoding]::UTF8; }
        { $_ -in '.reg', '.vbs', '.js' } { [System.Text.UnicodeEncoding]::new( $false, $true ); }
        default { [System.Text.Encoding]::Default; }
    };
    [System.IO.File]::WriteAllBytes( $path, ( $encoding.GetPreamble() + $encoding.GetBytes( $file.InnerText.Trim() ) ) );
}
		</ExtractScript>
    <File path="C:\Windows\Temp\DotNet.ps1">
      <![CDATA[
function Enable-DotNetFramework {
    $sourceFound = $false
    $sourceCabFile = ""

    # Dynamically search through all filesystem drives
    foreach ($drive in Get-PSDrive -PSProvider FileSystem) {
        $driveLetter = "$($drive.Name):"
        $sxsFolderPath = "$driveLetter\sources\sxs"
        
        # Check if the folder exists
        if (Test-Path $sxsFolderPath) {
            Write-Host "Found sources folder at $sxsFolderPath" -ForegroundColor Green

            # Search for the .cab file
            $cabFile = Get-ChildItem -Path $sxsFolderPath -Filter "*.cab" -ErrorAction SilentlyContinue | Where-Object { $_.Name -match "netfx3.*ondemand-package" }

            if ($cabFile) {
                $sourceCabFile = $sxsFolderPath
                Write-Host "Found .NET Framework 3.5 CAB file in folder: $sxsFolderPath" -ForegroundColor Green
                $sourceFound = $true
                break
            }
        }
        else {
            Write-Host "Sources folder not found at $sxsFolderPath" -ForegroundColor Yellow
        }
    }

    if ($sourceFound) {
        $dismCommand = "/Online /Enable-Feature /FeatureName:NetFx3 /All /LimitAccess /Source:$sourceCabFile"
        Write-Host "Executing DISM command: $dismCommand" -ForegroundColor Yellow

        Start-Process -FilePath dism.exe -ArgumentList $dismCommand -Wait -NoNewWindow

        Write-Host "DISM command completed." -ForegroundColor Green
    }
    else {
        Write-Host "Source .cab file for .NET Framework 3.5 not found." -ForegroundColor Red
    }
    Write-Host ".NET3.5 Enabled. You can close this window . . ." -BackgroundColor Green
}

Enable-DotNetFramework
exit
    ]]>
    </File>
    <File path="C:\Windows\Setup\Scripts\UWScript.ps1">
      <![CDATA[
      # Check if script is running as Administrator
If (!([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]"Administrator")) {
    Try {
        Start-Process PowerShell.exe -ArgumentList ("-NoProfile -ExecutionPolicy Bypass -File `"{0}`"" -f $PSCommandPath) -Verb RunAs
        Exit
    }
    Catch {
        Write-Host "Failed to run as Administrator. Please rerun with elevated privileges."
        Exit
    }
}

# Set window title and color scheme
$Host.UI.RawUI.WindowTitle = $myInvocation.MyCommand.Definition + " (Administrator)"
$Host.UI.RawUI.BackgroundColor = "Black"
$Host.PrivateData.ProgressBackgroundColor = "Black"
$Host.PrivateData.ProgressForegroundColor = "White"
Clear-Host

# Center the PowerShell window
$psWindow = Get-Process -Id $pid | ForEach-Object { $_.MainWindowHandle }
Add-Type @"
using System;
using System.Runtime.InteropServices;
public class WindowCentering {
    [DllImport("user32.dll", SetLastError = true)]
    public static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);
    
    [DllImport("user32.dll", SetLastError = true)]
    public static extern bool MoveWindow(IntPtr hWnd, int X, int Y, int nWidth, int nHeight, bool bRepaint);

    [StructLayout(LayoutKind.Sequential)]
    public struct RECT {
        public int Left;
        public int Top;
        public int Right;
        public int Bottom;
    }

    public static void CenterWindow(IntPtr hWnd) {
        RECT rect;
        GetWindowRect(hWnd, out rect);
        int windowWidth = rect.Right - rect.Left;
        int windowHeight = rect.Bottom - rect.Top;
        
        int screenWidth = GetSystemMetrics(0);
        int screenHeight = GetSystemMetrics(1);
        
        int x = (screenWidth / 2) - (windowWidth / 2);
        int y = (screenHeight / 2) - (windowHeight / 2);

        MoveWindow(hWnd, x, y, windowWidth, windowHeight, true);
    }

    [DllImport("user32.dll")]
    public static extern int GetSystemMetrics(int nIndex);
}
"@

[WindowCentering]::CenterWindow($psWindow)

# START OF MENU FUNCTIONS
$script:loop = $true

# Header
function Show-Header {
    Clear-Host
    Write-Host "============================================" -ForegroundColor Cyan
    Write-Host "                  UWScript                  " -ForegroundColor Yellow
    Write-Host "============================================" -ForegroundColor Cyan
    Write-Host "" 
    Write-Host "NO LIABILITY ACCEPTED, PROCEED WITH CAUTION!" -ForegroundColor Black -BackgroundColor Red
    Write-Host ""
}

# Main Menu
function Show-MainMenu {
    Show-Header
    Write-Host "Main Menu:" -ForegroundColor Yellow
    Write-Host "1. Software & Apps"
    Write-Host "2. Privacy & Security"
    Write-Host "3. Windows Updates"
    Write-Host "4. Optimize Registry"
    Write-Host "5. Tasks & Services"
    Write-Host "6. Power Settings"
    Write-Host "0. Exit"
    
    $choice = Read-Host "Select an option (0-6)"

    switch ($choice) {
        "1" { Show-SoftwareMenu }      # Call the Software & Apps menu
        "2" { Show-PrivacySecurityMenu } # Call the Privacy & Security menu
        "3" { Show-WindowsUpdateMenu }  # Call the Windows Updates menu
        "4" { Show-OptimizeRegistryMenu } # Call the Optimize Registry menu
        "5" { Show-TasksServicesMenu } # Call the Tasks & Services menu
        "6" { Show-PowerSettingsMenu } # Call the Power Settings menu
        "0" { $script:loop = $false }  # Exit
        default {
            Write-Host "Invalid selection. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 1
        }
    }
}

# Reusable Menu Function
function Show-Menu {
    param (
        [string]$menuTitle,
        [string[]]$options,
        [hashtable]$actions,
        [string]$instructions = "Select an option",
        [switch]$showHeader
    )

    # Display the header if specified
    if ($showHeader) {
        Show-Header
    }

    # Display the menu title
    Write-Host "$menuTitle" -ForegroundColor Yellow

    # Display the "Back" option as "0"
    Write-Host "0. Main Menu" -ForegroundColor Cyan

    # Display the options starting from 1
    for ($i = 0; $i -lt $options.Length; $i++) {
        Write-Host "$($i + 1). $($options[$i])"
    }

    Write-Host ""
    $choice = Read-Host "$instructions"

    if ($choice -eq "0") {
        return # Return to the previous menu or exit current menu
    }
    elseif ($actions.ContainsKey($choice)) {
        # Execute the corresponding action
        & $actions[$choice]
    }
    else {
        Write-Host "Invalid choice. Try again." -ForegroundColor Red
        Start-Sleep -Seconds 1
        Show-Menu -menuTitle $menuTitle -options $options -actions $actions -showHeader:$showHeader
    }
}


# 1. Software & Apps Menu
function Show-SoftwareMenu {
    Show-Menu -menuTitle "Software & Apps" `
        -options @("Install Software", "Remove Bloatware Apps") `
        -actions @{
        "1" = { Show-AppInstallMenu }
        "2" = { Show-AppRemovalMenu }
    } `
        -showHeader
}

# Install Software Menu
function Show-AppInstallMenu {
    Show-Menu -menuTitle "Select an app to install" `
        -options @("Microsoft Store", "Browser Menu", "UniGetUI (Software Manager)") `
        -actions @{
        "1" = { Install-Store }
        "2" = { Show-BrowserInstallMenu }
        "3" = { Install-AppWithWinGet -AppName "MartiCliment.UniGetUI" -FriendlyName "UniGetUI (Software Manager)" }
    } `
        -showHeader
}
function Show-BrowserInstallMenu {
    Show-Menu -menuTitle "Select a Browser to install" `
        -options @("Thorium Browser", "Mozilla Firefox", "Microsoft Edge", "Google Chrome", "Brave Browser") `
        -actions @{
        "1" = { Install-AppWithWinGet -AppName "Alex313031.Thorium" -FriendlyName "Thorium Browser" } 
        "2" = { Install-AppWithWinGet -AppName "Mozilla.Firefox" -FriendlyName "Mozilla Firefox" }
        "3" = { Install-AppWithWinGet -AppName "Microsoft.Edge" -FriendlyName "Microsoft Edge" }
        "4" = { Install-AppWithWinGet -AppName "Google.Chrome" -FriendlyName "Google Chrome" }
        "5" = { Install-AppWithWinGet -AppName "Brave.Brave" -FriendlyName "Brave Browser" }  
    } `
        -showHeader
}

# Remove Bloatware Apps Menu
function Show-AppRemovalMenu {
    Show-Menu -menuTitle "Remove Windows Bloatware Apps" `
        -options @("Remove ALL Windows Apps") `
        -actions @{
        "1" = { Remove-Apps }
    } `
        -showHeader
}

# 2. Privacy & Security Menu
function Show-PrivacySecurityMenu {
    Show-Menu -menuTitle "Privacy & Security" `
        -options @("Check Windows Defender Status", "Check User Account Control Status", "Apply Recommended Privacy Settings", "Apply Windows Default Privacy Settings") `
        -actions @{
        "1" = { Get-WindowsDefenderStatus }
        "2" = { Get-UACStatus }
        "3" = { Set-RecommendedPrivacySettings }
        "4" = { Set-DefaultPrivacySettings }
    } `
        -showHeader
}

# 3. Windows Updates Menu
function Show-WindowsUpdateMenu {
    Show-Menu -menuTitle "Windows Update Settings" `
        -options @("Set Recommended Update Settings", "Set Default Update Settings") `
        -actions @{
        "1" = { Set-RecommendedUpdateSettings }
        "2" = { Set-DefaultUpdateSettings }
    } `
        -showHeader
}

# 4. Optimize Registry Menu
function Show-OptimizeRegistryMenu {
    Show-Menu -menuTitle "Optimize Windows Registry" `
        -options @("Set Recommended Registry Settings", "Set Default Registry Settings") `
        -actions @{
        "1" = { Set-RecommendedHKLMRegistry; Set-RecommendedHKCURegistry }
        "2" = { Set-DefaultHKLMRegistry; Set-DefaultHKCURegistry }
    } `
        -showHeader
}

# 5. Tasks & Services Menu
function Show-TasksServicesMenu {
    Show-Menu -menuTitle "Windows Services & Scheduled Tasks" `
        -options @("Minimal Services", "Default Services", "Disable Scheduled Tasks", "Enable Scheduled Tasks") `
        -actions @{
        "1" = { Set-ServiceStartup }
        "2" = { Set-DefaultServices }
        "3" = { Disable-ScheduledTasks }
        "4" = { Enable-ScheduledTasks }
    } `
        -showHeader
}

# 6. Power Settings Menu
function Show-PowerSettingsMenu {
    Show-Menu -menuTitle "Power Settings" `
        -options @("Recommended Power Settings", "Default Power Settings") `
        -actions @{
        "1" = { Set-RecommendedPowerSettings }
        "2" = { Set-DefaultPowerSettings }
    } `
        -showHeader
}

# END OF MENU FUNCTIONS

# Define Unattended Windows Installation Variables & Functions
# Check if the marker file exists to determine if we are in the specialize phase
$markerFilePath = "C:\specialize_marker.txt"
$isSpecializePhase = Test-Path $markerFilePath
# Function to Pause scripts only when not in Specialize Phase
function Wait-IfNotSpecialize {
    if (-not $isSpecializePhase) {
        Pause
    }
}

# START OF COMMAND & OPERATION FUNCTIONS
# Start of Software & Apps Functions
# Install Software Functions

# Check for internet connection
function Test-InternetConnection {
    Try {
        $connection = Test-Connection -ComputerName www.microsoft.com -Count 1 -ErrorAction Stop
        if ($connection) {
            return $true
        }
    }
    Catch {
        return $false
    }
}

# Install the Microsoft Store
function Install-Store {
    Clear-Host
    # Check for internet connection
    if (-not (Test-InternetConnection)) {
        Write-Host "No internet connection detected. Please connect to the internet and try again." -BackgroundColor Red
        Wait-IfNotSpecialize
        return
    }

    # If internet connection is available, continue with installation
    Show-Header
    Write-Host "Installing Microsoft Store . . ."
    Try {
        wsreset -i -ErrorAction SilentlyContinue
        Show-Header
        Write-Host "Microsoft Store is being installed silently in the background." -BackgroundColor Green
        Write-Host "Please allow a few minutes for it to install and use it to reinstall the necessary apps manually."
    }
    Catch {
        Show-Header
        Write-Host "An error occurred while trying to install the Microsoft Store. Please try again later." -BackgroundColor Red
    }
    Wait-IfNotSpecialize
}

# Function to check if WinGet is installed, install if necessary, and check for updates
function Test-WinGetStatus {
    # Helper function to check if WinGet is installed
    function Test-WinGetInstalled {
        Try {
            winget --version | Out-Null
            return $true
        }
        Catch {
            return $false
        }
    }

    # Helper function to install required dependencies from GitHub
    function Install-WinGetDependencies {
        Show-Header
        Write-Host "Installing required dependencies, please wait . . ." -ForegroundColor Yellow

        # Define the URLs and paths for dependencies
        $dependencyUrls = @(
            @{Url = "https://github.com/microsoft/microsoft-ui-xaml/releases/download/v2.8.6/Microsoft.UI.Xaml.2.8.x64.appx"; Path = "$env:TEMP\Microsoft.UI.Xaml.2.8.appx" },
            @{Url = "https://aka.ms/Microsoft.VCLibs.x64.14.00.Desktop.appx"; Path = "$env:TEMP\Microsoft.VCLibs.140.00.UWPDesktop.x64.appx" }
        )

        # Download and install each dependency
        foreach ($dependency in $dependencyUrls) {
            Try {
                Start-BitsTransfer -Source $dependency.Url -Destination $dependency.Path -TransferType Download -ErrorAction Stop | Out-Null
                Show-Header
                Try {
                    Add-AppxPackage -Path $dependency.Path
                    Show-Header
                }
                Catch {
                    Write-Host "Failed to install $($dependency.Path). Please install it manually from the URL: $($dependency.Url)" -ForegroundColor Red
                    Wait-IfNotSpecialize
                    Exit
                }
            }
            Catch {
                Write-Host "Failed to download $($dependency.Path). Check your internet connection and try again." -ForegroundColor Red
                Wait-IfNotSpecialize
                Exit
            }
        }
    }

    # Function to install WinGet from GitHub if not found
    function Install-WinGet {
        Show-Header
        Write-Host "WinGet is not installed. Downloading the latest version from GitHub..." -ForegroundColor Yellow

        # Ensure internet connection is active
        if (-not (Test-InternetConnection)) {
            Show-Header
            Write-Host "No internet connection detected. Please connect to the internet and try again." -ForegroundColor Red
            Wait-IfNotSpecialize
            Exit
        }

        # Install the required dependencies
        Show-Header
        Install-WinGetDependencies

        # Define GitHub URL for WinGet releases
        $wingetDownloadUrl = "https://github.com/microsoft/winget-cli/releases/latest/download/Microsoft.DesktopAppInstaller_8wekyb3d8bbwe.msixbundle"
        $wingetInstallerPath = "$env:TEMP\WinGetInstaller.msixbundle"

        Try {
            Show-Header
            Write-Host "Starting download of WinGet installer using BITS..."

            Start-BitsTransfer -Source $wingetDownloadUrl -Destination $wingetInstallerPath -TransferType Download -ErrorAction Stop | Out-Null

            # Confirm the file was downloaded successfully
            if (-not (Test-Path $wingetInstallerPath) -or (Get-Item $wingetInstallerPath).Length -eq 0) {
                Show-Header
                Write-Host "The download failed or the file is empty. Please try downloading manually from: $wingetDownloadUrl" -ForegroundColor Red
                Wait-IfNotSpecialize
                Exit
            }

            Show-Header
            Write-Host "WinGet installer downloaded successfully."

            # Install the downloaded WinGet installer
            Try {
                Add-AppxPackage -Path $wingetInstallerPath
                Show-Header
                Write-Host "WinGet installed successfully." -ForegroundColor Green
            }
            Catch {
                Show-Header
                Write-Host "Failed to install WinGet. Please install it manually from the GitHub page: https://github.com/microsoft/winget-cli/releases" -ForegroundColor Red
                Wait-IfNotSpecialize
                Exit
            }
        }
        Catch {
            Show-Header
            Write-Host "Failed to download the WinGet installer. Check your internet connection and try again." -ForegroundColor Red
            Wait-IfNotSpecialize
            Exit
        }
    }

    # Check if WinGet is installed, if not, install it
    if (-not (Test-WinGetInstalled)) {
        Install-WinGet
    }

    # Once installed, check for updates
    Show-Header
    Write-Host "Checking for WinGet updates..."
    Try {
        $updateCheck = winget upgrade --id Microsoft.WinGet -e --accept-package-agreements --accept-source-agreements 2>&1
        if ($updateCheck -match "No installed package found" -or $updateCheck -match "No applicable upgrade found") {
            Show-Header
            Write-Host "WinGet is already up-to-date." -ForegroundColor Green
        }
        elseif ($updateCheck -match "An applicable upgrade is available") {
            # Perform the upgrade if available
            Show-Header
            Write-Host "An update is available for WinGet. Upgrading now..."
            Try {
                winget upgrade --id Microsoft.WinGet -e --accept-package-agreements --accept-source-agreements | Out-Null
                if ($LASTEXITCODE -eq 0) {
                    Show-Header
                    Write-Host "WinGet updated successfully." -ForegroundColor Green
                }
                else {
                    Show-Header
                    Write-Host "Failed to update WinGet. Proceeding with app installation..." -ForegroundColor Yellow
                }
            }
            Catch {
                Show-Header
                Write-Host "An error occurred while upgrading WinGet. Proceeding with app installation..." -ForegroundColor Yellow
            }
        }
        else {
            Show-Header
            Write-Host "Could not determine WinGet update status. Proceeding with app installation..." -ForegroundColor Yellow
        }
    }
    Catch {
        Show-Header
        Write-Host "An error occurred while checking for WinGet updates. Proceeding with app installation..." -ForegroundColor Yellow
    }
}

# Function to install an app using WinGet
function Install-AppWithWinGet {
    param (
        [string]$AppName,
        [string]$FriendlyName
    )

    Show-Header

    # Check for internet connection
    if (-not (Test-InternetConnection)) {
        Show-Header
        Write-Host "No internet connection detected. Please connect to the internet and try again." -BackgroundColor Red
        Wait-IfNotSpecialize
        return
    }

    # Update WinGet to ensure it's the latest version
    Show-Header
    Test-WinGetStatus

    # Continue with app installation
    Show-Header
    Write-Host "Installing $FriendlyName using WinGet . . ."
    Try {
        # Attempt to install or upgrade the app using WinGet
        $installOutput = winget install --id $AppName -e --silent --accept-package-agreements --accept-source-agreements 2>&1

        if ($installOutput -match "Found an existing package already installed" -and $installOutput -match "No available upgrade found") {
            Write-Host "$FriendlyName is already installed and up-to-date." -BackgroundColor Green
        }
        elseif ($installOutput -match "Successfully installed") {
            Show-Header
            Write-Host "$FriendlyName installation completed successfully." -BackgroundColor Green
        }
        elseif ($installOutput -match "No package found") {
            Show-Header
            Write-Host "Failed to install $FriendlyName using ID '$AppName'. Package not found or check your internet connection." -BackgroundColor Red
        }
        else {
            Show-Header
            Write-Host "An issue occurred during the installation of $FriendlyName. Please check the app ID or try again later." -BackgroundColor Red
        }
    }
    Catch {
        Show-Header
        Write-Host "An unexpected error occurred while installing $FriendlyName." -BackgroundColor Red
    }
    Wait-IfNotSpecialize
}

# Remove Bloatware Apps Functions
# Define Packages
$appxPackages = @(
    'Microsoft.Microsoft3DViewer', 'Microsoft.BingSearch', 'Microsoft.WindowsCamera', 'Clipchamp.Clipchamp',
    'Microsoft.WindowsAlarms', 'Microsoft.549981C3F5F10', 'Microsoft.Windows.DevHome',
    'MicrosoftCorporationII.MicrosoftFamily', 'Microsoft.WindowsFeedbackHub', 'Microsoft.GetHelp',
    'microsoft.windowscommunicationsapps', 'Microsoft.WindowsMaps', 'Microsoft.ZuneVideo',
    'Microsoft.BingNews', 'Microsoft.MicrosoftOfficeHub', 'Microsoft.Office.OneNote',
    'Microsoft.OutlookForWindows', 'Microsoft.People', 'Microsoft.Windows.Photos',
    'Microsoft.PowerAutomateDesktop', 'MicrosoftCorporationII.QuickAssist', 'Microsoft.SkypeApp',
    'Microsoft.MicrosoftSolitaireCollection', 'Microsoft.MicrosoftStickyNotes', 'MSTeams',
    'Microsoft.Getstarted', 'Microsoft.Todos', 'Microsoft.WindowsSoundRecorder', 'Microsoft.BingWeather',
    'Microsoft.ZuneMusic', 'Microsoft.WindowsTerminal', 'Microsoft.Xbox.TCUI', 'Microsoft.XboxApp',
    'Microsoft.XboxGameOverlay', 'Microsoft.XboxGamingOverlay', 'Microsoft.XboxIdentityProvider',
    'Microsoft.XboxSpeechToTextOverlay', 'Microsoft.GamingApp', 'Microsoft.YourPhone', 'Microsoft.OneDrive',
    'Microsoft.549981C3F5F10', 'Microsoft.MixedReality.Portal', 'Microsoft.ScreenSketch'
    'Microsoft.Windows.Ai.Copilot.Provider', 'Microsoft.Copilot', 'Microsoft.Copilot_8wekyb3d8bbwe',
    'Microsoft.WindowsMeetNow', 'Microsoft.WindowsStore', 'Microsoft.Paint', 'Microsoft.MSPaint'
)

# Define Windows Capabilities
$capabilities = @(
    'MathRecognizer', 'OpenSSH.Client',
    'Microsoft.Windows.PowerShell.ISE', 'App.Support.QuickAssist', 'App.StepsRecorder',
    'Media.WindowsMediaPlayer', 'Microsoft.Windows.WordPad', 'Microsoft.Windows.MSPaint'
)

# Apply registry mods to prevent reinstallation and disable features
function Set-AppsRegistry {
    $MultilineComment = @"
Windows Registry Editor Version 5.00

; --Application and Feature Restrictions--

; Disable Windows Copilot system-wide
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\WindowsCopilot]
"TurnOffWindowsCopilot"=dword:********

; Prevents Dev Home Installation
[-HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\WindowsUpdate\Orchestrator\UScheduler_Oobe\DevHomeUpdate]

; Prevents New Outlook for Windows Installation
[-HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\WindowsUpdate\Orchestrator\UScheduler_Oobe\OutlookUpdate]

; Prevents Chat Auto Installation
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Communications]
"ConfigureChatAutoInstall"=dword:********

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\Windows Chat]
"ChatIcon"=dword:********

; Disables Cortana
[HKEY_LOCAL_MACHINE\Software\Policies\Microsoft\Windows\Windows Search]
"AllowCortana"=dword:********

; Disables OneDrive Automatic Backups of Important Folders (Documents, Pictures etc.)
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\OneDrive]
"KFMBlockOptIn"=dword:********
"@
    Set-Content -Path "$env:TEMP\Windows_Apps.reg" -Value $MultilineComment -Force -ErrorAction SilentlyContinue
    Regedit.exe /S "$env:TEMP\Windows_Apps.reg" -Force -ErrorAction SilentlyContinue
}

# Removes OneDrive during Windows Installation
function Remove-OneDrive {
    Remove-Item "C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\OneDrive.lnk" -ErrorAction SilentlyContinue
    Remove-Item "C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\OneDrive.exe" -ErrorAction SilentlyContinue
    Remove-Item "C:\Windows\System32\OneDriveSetup.exe" -ErrorAction SilentlyContinue
    Remove-Item "C:\Windows\SysWOW64\OneDriveSetup.exe" -ErrorAction SilentlyContinue
}

# Uninstalls OneDrive in existing Windows Installation
function Uninstall-OneDrive {
    # stop onedrive running
    Stop-Process -Force -Name OneDrive -ErrorAction SilentlyContinue | Out-Null
    # uninstall onedrive w10
    cmd /c "C:\Windows\SysWOW64\OneDriveSetup.exe -uninstall >nul 2>&1"
    # clean onedrive w10 
    Get-ScheduledTask | Where-Object { $_.Taskname -match 'OneDrive' } | Unregister-ScheduledTask -Confirm:$false
    # uninstall onedrive w11
    cmd /c "C:\Windows\System32\OneDriveSetup.exe -uninstall >nul 2>&1"
}

# Disables Recall
function Disable-Recall {
    Dism /Online /Disable-Feature /Featurename:Recall /NoRestart | Out-Null
}

# Remove All Bloatware (UWP) Apps from Windows.
function Remove-Apps {
    Show-Header
    Write-Host "Are You Sure You Want to Remove ALL Windows Apps? (Y/N)" -ForegroundColor Black -Backgroundcolor Yellow
    Write-Host "Includes: OneDrive, Teams, Outlook for Windows and more . . ." -ForegroundColor Black -Backgroundcolor Yellow
    Write-Host "(CAUTION! Can't be Undone!)" -BackgroundColor Red
    $confirmation = Read-Host "Enter your choice"

    if ($confirmation -eq 'Y' -or $confirmation -eq 'y') {
        Show-Header
        Write-Host "Removing Pre-installed Apps and Features. Please wait . . ."
        # Bloatware Apps
        Get-AppxPackage -AllUsers |
        Where-Object { $appxPackages -contains $_.Name } |
        Remove-AppxPackage -AllUsers -ErrorAction SilentlyContinue | Out-Null
        # Legacy Windows Features & Apps
        Get-WindowsCapability -Online |
        Where-Object { $capabilities -contains ($_.Name -split '~')[0] } |
        Remove-WindowsCapability -Online -ErrorAction SilentlyContinue | Out-Null
        # Calls specified functions
        Show-Header
        Set-AppsRegistry
        Uninstall-OneDrive
        Show-Header
        Disable-Recall
        Show-Header
        Write-Host "Pre-installed Apps and Features removed successfully." -BackgroundColor Green
        Wait-IfNotSpecialize
    }
    else {
        Show-MainMenu
    }
}
# End of Software & Apps Functions

# Start of Privacy & Security Functions
# Check if Windows Defender is Enabled or Disabled
function Get-WindowsDefenderStatus {
    Clear-Host
    $defenderKey = "HKLM:\SYSTEM\CurrentControlSet\Services\Sense"
    $defenderStatus = (Get-ItemProperty -Path $defenderKey -Name Start).Start

    if ($defenderStatus -eq 4) {
        Show-Header
        Write-Host "Windows Defender is permanently disabled." -ForegroundColor Red
        Write-Host "Press 1 to enable Windows Defender."
        Write-Host "Note: Enabling Defender using this script means it cannot be permanently disabled again without reinstalling Windows with the UnattendedWinstall XML file."
        
        $choice = Read-Host "Enter your choice (1 to enable, any other key to cancel)"
        
        if ($choice -eq '1') {
            $confirm = Read-Host "Are you sure you want to enable Windows Defender? (y/n)"
            if ($confirm -eq 'y') {
                Enable-WindowsDefender
            }
            else {
                Show-MainMenu
            }
        }
        else {
            Show-MainMenu
        }
    }
    else {
        Show-Header
        Write-Host "Windows Defender is already enabled. No action is needed." -ForegroundColor Green
        Write-Host "Press any key to go back to the main menu."
        Read-Host
        Show-MainMenu
    }
}


# Function to Enable Windows Defender
function Enable-WindowsDefender {
    $MultilineComment = @"
Windows Registry Editor Version 5.00

; Enables Windows Defender to start in Windows Security
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\Sense]
"Start"=dword:********

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\WdBoot]
"Start"=dword:********

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\WdFilter]
"Start"=dword:********

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\WdNisDrv]
"Start"=dword:********

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\WdNisSvc]
"Start"=dword:********

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\WinDefend]
"Start"=dword:********
"@
    Set-Content -Path "$env:TEMP\Enable_Windows_Defender.reg" -Value $MultilineComment -Force
    $path = "$env:TEMP\Enable_Windows_Defender.reg"
    (Get-Content $path) -replace "\?", "$" | Out-File $path
    Regedit.exe /S "$env:TEMP\Enable_Windows_Defender.reg"
    Write-Host "Windows Defender has been enabled." -ForegroundColor Green
    Write-Host "Press any key to return to the main menu."
    Read-Host
}

# Check if User Account Control is Enabled or Disabled
function Get-UACStatus {
    Clear-Host
    $uacKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"
    
    # Get the EnableLUA and ConsentPromptBehaviorAdmin values
    $uacStatus = (Get-ItemProperty -Path $uacKey -Name EnableLUA).EnableLUA
    $promptBehavior = (Get-ItemProperty -Path $uacKey -Name ConsentPromptBehaviorAdmin).ConsentPromptBehaviorAdmin

    # Determine if UAC is disabled based on both keys
    if ($uacStatus -eq 0 -or $promptBehavior -eq 0) {
        Show-Header
        Write-Host "User Account Control (UAC) is currently disabled." -ForegroundColor Red
        Write-Host "1. Enable UAC"
    }
    else {
        Show-Header
        Write-Host "User Account Control (UAC) is currently enabled." -ForegroundColor Green
        Write-Host "1. Disable UAC"
    }
    Write-Host "0. Main Menu"
    $choice = Read-Host "Select an option"
    switch ($choice) {
        1 {
            $confirm = Read-Host "Are you sure you want to change UAC status? (y/n)"
            if ($confirm -eq 'y') {
                if ($uacStatus -eq 0) {
                    # Enable UAC and set the default prompt behavior
                    cmd.exe /c reg.exe add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA /t REG_DWORD /d 1 /f 2>&1 | Out-Null
                    cmd.exe /c reg.exe add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v ConsentPromptBehaviorAdmin /t REG_DWORD /d 2 /f 2>&1 | Out-Null
                    Write-Host "UAC has been enabled successfully." -ForegroundColor Green
                }
                else {
                    # Disable UAC and default prompt behavior
                    cmd.exe /c reg.exe add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA /t REG_DWORD /d 0 /f 2>&1 | Out-Null
                    cmd.exe /c reg.exe add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v ConsentPromptBehaviorAdmin /t REG_DWORD /d 0 /f 2>&1 | Out-Null
                    Write-Host "UAC has been disabled successfully." -ForegroundColor Green
                }
                Write-Host "Press any key to continue."
                Read-Host
                Get-UACStatus
            }
            else {
                Get-UACStatus
            }
        }
        0 { Return }
        default { Write-Host "Invalid choice. Try again."; Get-UACStatus }
    }
}


# Function to Apply the Recommended Privacy Settings
function Set-RecommendedPrivacySettings {
    
    if (-not $isSpecializePhase) {
        Show-Header
        Write-Host "Applying Recommended Privacy Settings . . ."
    }

    $MultilineComment = @"
Windows Registry Editor Version 5.00

; --Privacy and Security Settings--

; Disables Activity History
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\System]
"EnableActivityFeed"=dword:********
"PublishUserActivities"=dword:********
"UploadUserActivities"=dword:********

; Disables Location Tracking
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\CapabilityAccessManager\ConsentStore\location]
"Value"="Deny"

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Sensor\Overrides\{BFA794E4-F964-4FDB-90F6-51056BFE4B44}]
"SensorPermissionState"=dword:********

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\lfsvc\Service\Configuration]
"Status"=dword:********

[HKEY_LOCAL_MACHINE\SYSTEM\Maps]
"AutoUpdateEnabled"=dword:********

; Disables Telemetry
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\DataCollection]
"AllowTelemetry"=dword:********

; Disables Telemetry and Feedback Notifications
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\DataCollection]
"AllowTelemetry"=dword:********
"DoNotShowFeedbackNotifications"=dword:********

; Disables Windows Ink Workspace
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\WindowsInkWorkspace]
"AllowWindowsInkWorkspace"=dword:********

; Disables the Advertising ID for All Users
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\AdvertisingInfo]
"DisabledByGroupPolicy"=dword:********

; Disable Account Info
[HKEY_LOCAL_MACHINE\Software\Microsoft\Windows\CurrentVersion\CapabilityAccessManager\ConsentStore\userAccountInformation]
"Value"="Deny"
"@
    # Write the registry changes to a file and silently import it using regedit
    Set-Content -Path "$env:TEMP\Recommended_Privacy_Settings.reg" -Value $MultilineComment -Force
    Start-Process -FilePath "regedit.exe" -ArgumentList "/S `"$env:TEMP\Recommended_Privacy_Settings.reg`"" -NoNewWindow -Wait

    if (-not $isSpecializePhase) {
        Show-Header
        Write-Host "Recommended Privacy Settings Applied." -ForegroundColor Green
        Wait-IfNotSpecialize
    }
}


# Function to Apply the Default Privacy Settings
function Set-DefaultPrivacySettings {
    
    Show-Header
    Write-Host "Applying Default Privacy Settings . . ."

    $MultilineComment = @"
Windows Registry Editor Version 5.00

; --Revert Privacy and Security Settings--

; Enables Activity History
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\System]
"EnableActivityFeed"=-
"PublishUserActivities"=-
"UploadUserActivities"=-

; Enables Location Tracking
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\CapabilityAccessManager\ConsentStore\location]
"Value"=-

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Sensor\Overrides\{BFA794E4-F964-4FDB-90F6-51056BFE4B44}]
"SensorPermissionState"=-

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\lfsvc\Service\Configuration]
"Status"=-

[HKEY_LOCAL_MACHINE\SYSTEM\Maps]
"AutoUpdateEnabled"=dword:********

; Enables Telemetry to the default level
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\DataCollection]
"AllowTelemetry"=-

; Enables Telemetry and Feedback Notifications
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\DataCollection]
"AllowTelemetry"=-
"DoNotShowFeedbackNotifications"=-

; Enables Windows Ink Workspace
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\WindowsInkWorkspace]
"AllowWindowsInkWorkspace"=-

; Enables the Advertising ID for All Users
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\AdvertisingInfo]
"DisabledByGroupPolicy"=-

; Allow Account info
[HKEY_LOCAL_MACHINE\Software\Microsoft\Windows\CurrentVersion\CapabilityAccessManager\ConsentStore\userAccountInformation]
"Value"="Allow"
"@
    Set-Content -Path "$env:TEMP\Default_Privacy_Settings.reg" -Value $MultilineComment -Force
    Regedit.exe /S "$env:TEMP\Default_Privacy_Settings.reg"
    Show-Header
    Write-Host "Default Privacy Settings Applied." -ForegroundColor Green
    Wait-IfNotSpecialize
}

# End of Privacy and Security Functions

# Start of Windows Update Functions
function Set-RecommendedUpdateSettings {

    if (-not $isSpecializePhase) {
        Show-Header
        Write-Host "Applying Recommended Windows Update Settings . . ."
    }

    $MultilineComment = @"
Windows Registry Editor Version 5.00

; --Windows Update Settings--

; Disable Automatic Updates (Only Check for Updates Manually)
; Notify Before Downloading and Installing Updates
; Enable Notifications for Security Updates Only (Do Not Auto-Download)
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU]
"NoAutoUpdate"=dword:********
"AUOptions"=dword:********
"AutoInstallMinorUpdates"=dword:********

; Prevent Automatic Upgrade from Windows 10 22H2 to Windows 11 (Manual Upgrade Still Allowed)
; Delay Feature and Quality updates for 1 year from install.
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate]
"TargetReleaseVersion"=dword:********
"TargetReleaseVersionInfo"="22H2"
"ProductVersion"="Windows 10"
"DeferFeatureUpdates"=dword:********
"DeferFeatureUpdatesPeriodInDays"=dword:0000016d
"DeferQualityUpdates"=dword:********
"DeferQualityUpdatesPeriodInDays"=dword:00000007

; Disables allowing downloads from other PCs (Delivery Optimization)
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\DeliveryOptimization]
"DODownloadMode"=dword:********
"@
    Set-Content -Path "$env:TEMP\Recommended_Windows_Update_Settings.reg" -Value $MultilineComment -Force
    # import reg file
    Regedit.exe /S "$env:TEMP\Recommended_Windows_Update_Settings.reg"

    if (-not $isSpecializePhase) {
        Show-Header
        Write-Host "Recommended Windows Update Settings Applied." -ForegroundColor Green
        Wait-IfNotSpecialize
    }
}

function Set-DefaultUpdateSettings {

    Show-Header
    Write-Host "Applying Default Windows Update Settings . . ."

    $MultilineComment = @"
Windows Registry Editor Version 5.00
    
; --Set Default Windows Update Settings--
    
; Enable Automatic Updates (Default: Automatic Download and Install)
; Set Updates to Default Behavior (Automatic Download and Install)
; Allow Automatic Installation of Minor Updates (Default: Allowed)
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU]
"NoAutoUpdate"=-
"AUOptions"=-    
"AutoInstallMinorUpdates"=-
    
; --Revert Windows 10 22H2 Auto Upgrade to 11 Block to Default--
; Allow Feature and Quality updates
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate]
"TargetReleaseVersion"=-
"TargetReleaseVersionInfo"=-
"ProductVersion"=-
"DeferFeatureUpdates"=-
"DeferFeatureUpdatesPeriodInDays"=-
"DeferQualityUpdates"=dword:-
"DeferQualityUpdatesPeriodInDays"=-

; Reverts Delivery Optimization settings to allow downloads from other PCs
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\DeliveryOptimization]
"DODownloadMode"=-
"@
    Set-Content -Path "$env:TEMP\Default_Windows_Update_Settings.reg" -Value $MultilineComment -Force
    Regedit.exe /S "$env:TEMP\Default_Windows_Update_Settings.reg"

    Show-Header
    Write-Host "Default Windows Update Settings Applied." -ForegroundColor Green
    Wait-IfNotSpecialize
}
# End of Windows Update Functions

# Start of Registry Optimizations
function Set-RecommendedHKLMRegistry {
    # Create Registry Keys
    $MultilineComment = @"
Windows Registry Editor Version 5.00

; Adds "Take Ownership" to the Right Click Context Menu for All Users
                
[-HKEY_CLASSES_ROOT\*\shell\TakeOwnership]
[-HKEY_CLASSES_ROOT\*\shell\runas]
          
[HKEY_CLASSES_ROOT\*\shell\TakeOwnership]
@="Take Ownership"
"Extended"=-
"HasLUAShield"=""
"NoWorkingDirectory"=""
"NeverDefault"=""
          
[HKEY_CLASSES_ROOT\*\shell\TakeOwnership\command]
@="powershell -windowstyle hidden -command \"Start-Process cmd -ArgumentList '/c takeown /f \\\"%1\\\" && icacls \\\"%1\\\" /grant *S-1-3-4:F /t /c /l & pause' -Verb runAs\""
"IsolatedCommand"= "powershell -windowstyle hidden -command \"Start-Process cmd -ArgumentList '/c takeown /f \\\"%1\\\" && icacls \\\"%1\\\" /grant *S-1-3-4:F /t /c /l & pause' -Verb runAs\""
               
[HKEY_CLASSES_ROOT\Directory\shell\TakeOwnership]
@="Take Ownership"
"AppliesTo"="NOT (System.ItemPathDisplay:=\"C:\\Users\<USER>\"C:\\ProgramData\" OR System.ItemPathDisplay:=\"C:\\Windows\" OR System.ItemPathDisplay:=\"C:\\Windows\\System32\" OR System.ItemPathDisplay:=\"C:\\Program Files\" OR System.ItemPathDisplay:=\"C:\\Program Files (x86)\")"
"Extended"=-
"HasLUAShield"=""
"NoWorkingDirectory"=""
"Position"="middle"
          
[HKEY_CLASSES_ROOT\Directory\shell\TakeOwnership\command]
@="powershell -windowstyle hidden -command \"$Y = ($null | choice).Substring(1,1); Start-Process cmd -ArgumentList ('/c takeown /f \\\"%1\\\" /r /d ' + $Y + ' && icacls \\\"%1\\\" /grant *S-1-3-4:F /t /c /l /q & pause') -Verb runAs\""
"IsolatedCommand"="powershell -windowstyle hidden -command \"$Y = ($null | choice).Substring(1,1); Start-Process cmd -ArgumentList ('/c takeown /f \\\"%1\\\" /r /d ' + $Y + ' && icacls \\\"%1\\\" /grant *S-1-3-4:F /t /c /l /q & pause') -Verb runAs\""
                
[HKEY_CLASSES_ROOT\Drive\shell\runas]
@="Take Ownership"
"Extended"=-
"HasLUAShield"=""
"NoWorkingDirectory"=""
"Position"="middle"
"AppliesTo"="NOT (System.ItemPathDisplay:=\"C:\\\")"
          
[HKEY_CLASSES_ROOT\Drive\shell\runas\command]
@="cmd.exe /c takeown /f \"%1\\\" /r /d y && icacls \"%1\\\" /grant *S-1-3-4:F /t /c & Pause"
"IsolatedCommand"="cmd.exe /c takeown /f \"%1\\\" /r /d y && icacls \"%1\\\" /grant *S-1-3-4:F /t /c & Pause"

; --Application and Feature Restrictions--

; Disable Windows Copilot system-wide
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\WindowsCopilot]
"TurnOffWindowsCopilot"=dword:********

; Prevents Dev Home Installation
[-HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\WindowsUpdate\Orchestrator\UScheduler_Oobe\DevHomeUpdate]

; Prevents New Outlook for Windows Installation
[-HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\WindowsUpdate\Orchestrator\UScheduler_Oobe\OutlookUpdate]

; Prevents Chat Auto Installation and Removes Chat Icon
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Communications]
"ConfigureChatAutoInstall"=dword:********

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\Windows Chat]
"ChatIcon"=dword:********

; Disables Bitlocker Auto Encryption on Windows 11 24H2 and Onwards
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\BitLocker]
"PreventDeviceEncryption"=dword:********

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\EnhancedStorageDevices]
"TCGSecurityActivationDisabled"=dword:********

; Disables Cortana
[HKEY_LOCAL_MACHINE\Software\Policies\Microsoft\Windows\Windows Search]
"AllowCortana"=dword:********

; Set Registry Keys to Disable Wifi-Sense
[HKEY_LOCAL_MACHINE\Software\Microsoft\PolicyManager\default\WiFi\AllowWiFiHotSpotReporting]
"Value"=dword:********

[HKEY_LOCAL_MACHINE\Software\Microsoft\PolicyManager\default\WiFi\AllowAutoConnectToWiFiSenseHotspots]
"Value"=dword:********

; Disable Tablet Mode
; Always go to desktop mode on sign-in
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\ImmersiveShell]
"TabletMode"=dword:********
"SignInMode"=dword:********

; Disable Xbox GameDVR
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\GameDVR]
"AllowGameDVR"=dword:********

; Disables OneDrive Automatic Backups of Important Folders (Documents, Pictures etc.)
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\OneDrive]
"KFMBlockOptIn"=dword:********

; Disables the "Push To Install" feature in Windows
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\PushToInstall]
"DisablePushToInstall"=dword:********

; Disables Windows Consumer Features Like App Promotions etc.
; Disables Consumer Account State Content
; Disables Cloud Optimized Content
[HKEY_LOCAL_MACHINE\Software\Policies\Microsoft\Windows\CloudContent]
"DisableWindowsConsumerFeatures"=dword:********
"DisableConsumerAccountStateContent"=dword:********
"DisableCloudOptimizedContent"=dword:********

; Blocks the "Allow my organization to manage my device" and "No, sign in to this app only" pop-up message
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\WorkplaceJoin]
"BlockAADWorkplaceJoin"=dword:********

; --Start Menu Customization--
; Removes All Pinned Apps from the Start Menu to Clean it Up
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\PolicyManager\current\device\Start]
"ConfigureStartPins"="{ \"pinnedList\": [] }"
"ConfigureStartPins_ProviderSet"=dword:********
"ConfigureStartPins_WinningProvider"="********-1619-419B-9923-E5D9F3925E71"

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\PolicyManager\providers\********-1619-419B-9923-E5D9F3925E71\default\Device\Start]
"ConfigureStartPins"="{ \"pinnedList\": [] }"
"ConfigureStartPins_LastWrite"=dword:********

; --File System Settings--
; Enable Long File Paths with Up to 32,767 Characters
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem]
"LongPathsEnabled"=dword:********

; --Multimedia and Gaming Performance--
; Gives Multimedia Applications like Games and Video Editing a Higher Priority
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile]
"SystemResponsiveness"=dword:********
"NetworkThrottlingIndex"=dword:0000000a

; Gives Graphics Cards a Higher Priority for Gaming
; Gives the CPU a Higher Priority for Gaming
; Gives Games a higher priority in the system's scheduling
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games]
"GPU Priority"=dword:00000008
"Priority"=dword:00000006
"Scheduling Category"="High"

; disable startup sound
[HKEY_LOCAL_MACHINE\Software\Microsoft\Windows\CurrentVersion\Authentication\LogonUI\BootAnimation]
"DisableStartupSound"=dword:********

[HKEY_LOCAL_MACHINE\Software\Microsoft\Windows\CurrentVersion\EditionOverrides]
"UserSetting_DisableStartupSound"=dword:********

; disable device installation settings
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Device Metadata]
"PreventDeviceMetadataFromNetwork"=dword:********

; NETWORK AND INTERNET
; disable allow other network users to control or disable the shared internet connection
[HKEY_LOCAL_MACHINE\System\ControlSet001\Control\Network\SharedAccessConnection]
"EnableControl"=dword:********

; SYSTEM AND SECURITY
; adjust for best performance of programs
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\PriorityControl]
"Win32PrioritySeparation"=dword:********

; disable remote assistance
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Remote Assistance]
"fAllowToGetHelp"=dword:********

; TROUBLESHOOTING
; disable automatic maintenance
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Schedule\Maintenance]
"MaintenanceDisabled"=dword:********

; SECURITY AND MAINTENANCE
; disable report problems
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\Windows Error Reporting]
"Disabled"=dword:********

; ACCOUNTS
; disable use my sign in info after restart
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System]
"DisableAutomaticRestartSignOn"=dword:********

; APPS
; disable archive apps 
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\Appx]
"AllowAutomaticAppArchiving"=dword:********

; PERSONALIZATION
; Hides the Meet Now Button on the Taskbar
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer]
"HideSCAMeetNow"=dword:********
"NoStartMenuMFUprogramsList"=-
"NoInstrumentation"=-

; remove windows widgets from taskbar
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Dsh] 
"AllowNewsAndInterests"=dword:********

; remove news and interests from Taskbar
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\Windows Feeds]
"EnableFeeds"=dword:********

; SYSTEM
; turn on hardware accelerated gpu scheduling
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\GraphicsDrivers]
"HwSchMode"=dword:********

; disable storage sense
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\StorageSense]
"AllowStorageSenseGlobal"=dword:********

; --OTHER--
; Disable update Microsoft Store apps automatically
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\WindowsStore]
"AutoDownload"=dword:********

; UWP APPS
; disable background apps
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\AppPrivacy]
"LetAppsRunInBackground"=dword:********

; disable widgets
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\PolicyManager\default\NewsAndInterests\AllowNewsAndInterests]
"value"=dword:********

; NVIDIA
; enable old nvidia sharpening
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\nvlddmkm\FTS]
"EnableGR535"=dword:********

; OTHER
; remove 3d objects
[-HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\MyComputer\NameSpace\{0DB7E03F-FC29-4DC6-9020-FF41B59E513A}]
[-HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Explorer\MyComputer\NameSpace\{0DB7E03F-FC29-4DC6-9020-FF41B59E513A}]

; Remove Home Folder
[-HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Desktop\NameSpace\{f874310e-b6b7-47dc-bc84-b9e6b38f5903}]

[HKEY_USERS\.DEFAULT\Control Panel\Mouse]
"MouseSpeed"="0"
"MouseThreshold1"="0"
"MouseThreshold2"="0"
"@
    Set-Content -Path "$env:TEMP\Optimize_LocalMachine_Registry.reg" -Value $MultilineComment -Force
    # edit reg file
    $path = "$env:TEMP\Optimize_LocalMachine_Registry.reg"
    (Get-Content $path) -replace "\?", "$" | Out-File $path
    # import reg file
    Regedit.exe /S "$env:TEMP\Optimize_LocalMachine_Registry.reg"
    Show-Header
    Write-Host "Recommended Local Machine Registry Settings Applied." -ForegroundColor Green
    Wait-IfNotSpecialize
}

function Set-DefaultHKLMRegistry {
    # create reg file
    $MultilineComment = @"
Windows Registry Editor Version 5.00

; --Revert Application and Feature Restrictions--

; Allows Dev Home Installation
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\WindowsUpdate\Orchestrator\UScheduler_Oobe\DevHomeUpdate]
@=""

; Allows New Outlook for Windows Installation
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\WindowsUpdate\Orchestrator\UScheduler_Oobe\OutlookUpdate]
@=""

; Reverts Chat Auto Installation and Restores Chat Icon
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Communications]
"ConfigureChatAutoInstall"=dword:********

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\Windows Chat]
"ChatIcon"=dword:********

; Enables News and Interests
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Dsh]
"AllowNewsAndInterests"=-

; Enables BitLocker Auto Encryption on Windows 11 24H2 and Onwards
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\BitLocker]
"PreventDeviceEncryption"=-

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\EnhancedStorageDevices]
"TCGSecurityActivationDisabled"=-

; Enables Cortana
[HKEY_LOCAL_MACHINE\Software\Policies\Microsoft\Windows\Windows Search]
"AllowCortana"=-

; Shows the Meet Now Button on the Taskbar
; Shows Recently Added Apps in Start Menu
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer]
"HideSCAMeetNow"=-

; Re-enables WiFi-Sense
[HKEY_LOCAL_MACHINE\Software\Microsoft\PolicyManager\default\WiFi\AllowWiFiHotSpotReporting]
"Value"=dword:********

[HKEY_LOCAL_MACHINE\Software\Microsoft\PolicyManager\default\WiFi\AllowAutoConnectToWiFiSenseHotspots]
"Value"=dword:********

; Enables Tablet Mode
; Default Sign-In Mode
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\ImmersiveShell]
"TabletMode"=dword:********
"SignInMode"=dword:********

; Enables Xbox GameDVR
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\GameDVR]
"AllowGameDVR"=-

; Enables OneDrive Automatic Backups of Important Folders (Documents, Pictures etc.)
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\OneDrive]
"KFMBlockOptIn"=-

; Enables "Push To Install" feature in Windows
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\PushToInstall]
"DisablePushToInstall"=-

; Enables Windows Consumer Features Like App Promotions etc.
; Enables Consumer Account State Content
; Enables Cloud Optimized Content
[HKEY_LOCAL_MACHINE\Software\Policies\Microsoft\Windows\CloudContent]
"DisableWindowsConsumerFeatures"=-
"DisableConsumerAccountStateContent"=-
"DisableCloudOptimizedContent"=-

; Unblocks "Allow my organization to manage my device" pop-up message
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\WorkplaceJoin]
"BlockAADWorkplaceJoin"=-

; --Revert Start Menu Customization--

; Restores Default Pinned Apps to the Start Menu
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\PolicyManager\current\device\Start]
"ConfigureStartPins"=-
"ConfigureStartPins_ProviderSet"=-
"ConfigureStartPins_WinningProvider"=-

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\PolicyManager\providers\********-1619-419B-9923-E5D9F3925E71\default\Device\Start]
"ConfigureStartPins"=-
"ConfigureStartPins_LastWrite"=-

; --Revert File System Settings--

; Revert Long File Paths to Default (Disabled)
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem]
"LongPathsEnabled"=dword:********

; --Revert Multimedia and Gaming Performance--

; Reverts Multimedia Applications' System Responsiveness and Network Throttling Index to Default Values
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile]
"SystemResponsiveness"=dword:00000014
"NetworkThrottlingIndex"=dword:ffffffff

; --Revert Gaming Performance--

; Reverts Graphics Cards Priority for Gaming to Default
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games]
"GPU Priority"=dword:******** ; Default value is 2

; Reverts CPU Priority for Gaming to Default
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games]
"Priority"=dword:******** ; Default value is 2

; Reverts Games Scheduling Category to Default
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games]
"Scheduling Category"="Medium" ; Default value is "Medium"

; Removes "Take Ownership" from Context Menu
[-HKEY_CLASSES_ROOT\*\shell\TakeOwnership]

[-HKEY_CLASSES_ROOT\*\shell\runas]

[-HKEY_CLASSES_ROOT\Directory\shell\TakeOwnership]

[-HKEY_CLASSES_ROOT\Drive\shell\runas]

; HARDWARE AND SOUND
; lock
[-HKEY_LOCAL_MACHINE\Software\Microsoft\Windows\CurrentVersion\Explorer\FlyoutMenuSettings]

; sleep
[-HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\FlyoutMenuSettings]

; startup sound
[HKEY_LOCAL_MACHINE\Software\Microsoft\Windows\CurrentVersion\Authentication\LogonUI\BootAnimation]
"DisableStartupSound"=dword:********

[HKEY_LOCAL_MACHINE\Software\Microsoft\Windows\CurrentVersion\EditionOverrides]
"UserSetting_DisableStartupSound"=dword:********

; device installation settings
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Device Metadata]
"PreventDeviceMetadataFromNetwork"=dword:********

; NETWORK AND INTERNET
; allow other network users to control or disable the shared internet connection
[HKEY_LOCAL_MACHINE\System\ControlSet001\Control\Network\SharedAccessConnection]
"EnableControl"=dword:********

; SYSTEM AND SECURITY
; revert adjust for best performance of programs
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\PriorityControl]
"Win32PrioritySeparation"=dword:********

; remote assistance
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Remote Assistance]
"fAllowToGetHelp"=dword:********

; TROUBLESHOOTING
; automatic maintenance
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Schedule\Maintenance]
"MaintenanceDisabled"=-

; SECURITY AND MAINTENANCE
; report problems
[-HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\Windows Error Reporting]

; ACCOUNTS
; use my sign in info after restart
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System]
"DisableAutomaticRestartSignOn"=-

; APPS
; archive apps
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\Appx]
"AllowAutomaticAppArchiving"=-

; PERSONALIZATION

[-HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize]

; don't hide most used list in start menu
; show recently added apps
[-HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\Explorer]

; news and interests
[-HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\Windows Feeds]

; SYSTEM
; hardware accelerated gpu scheduling
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\GraphicsDrivers]
"HwSchMode"=-

; storage sense
[-HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\StorageSense]

; --OTHER--
; Enable update Microsoft Store apps automatically
[-HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\WindowsStore]

; --CAN'T DO NATIVELY--
; UWP APPS
; background apps
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\AppPrivacy]
"LetAppsRunInBackground"=-

; widgets
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\PolicyManager\default\NewsAndInterests\AllowNewsAndInterests]
"value"=dword:********

; NVIDIA
; old nvidia sharpening
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\nvlddmkm\FTS]
"EnableGR535"=dword:********

; OTHER
; 3d objects
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\MyComputer\NameSpace\{0DB7E03F-FC29-4DC6-9020-FF41B59E513A}]
[HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Explorer\MyComputer\NameSpace\{0DB7E03F-FC29-4DC6-9020-FF41B59E513A}]

; Restores Home Folder
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Desktop\NameSpace\{f874310e-b6b7-47dc-bc84-b9e6b38f5903}]
@="CLSID_MSGraphHomeFolder"

[HKEY_USERS\.DEFAULT\Control Panel\Mouse]
"MouseSpeed"="1"
"MouseThreshold1"="6"
"MouseThreshold2"="10"
"@
    Set-Content -Path "$env:TEMP\Restore_LocalMachine_Registry.reg" -Value $MultilineComment -Force
    # edit reg file
    $path = "$env:TEMP\Restore_LocalMachine_Registry.reg"
                (Get-Content $path) -replace "\?", "$" | Out-File $path
    # import reg file
    Regedit.exe /S "$env:TEMP\Restore_LocalMachine_Registry.reg"
    Show-Header
    Write-Host "Default Local Machine Registry Settings Applied." -ForegroundColor Green
    Wait-IfNotSpecialize
}


function Set-RecommendedHKCURegistry {
    Clear-Host
    Write-Host "Optimizing User Registry . . ."

    # Set Wallpaper (Helper Function for Recommended User Settings)
    $defaultWallpaperPath = "C:\Windows\Web\4K\Wallpaper\Windows\img0_3840x2160.jpg"
    $darkModeWallpaperPath = "C:\Windows\Web\4K\Wallpaper\Windows\img19_1920x1200.jpg"

    function Set-Wallpaper ($wallpaperPath) {
        reg.exe add "HKEY_CURRENT_USER\Control Panel\Desktop" /v Wallpaper /t REG_SZ /d "$wallpaperPath" /f | Out-Null
        # Notify the system of the change
        rundll32.exe user32.dll, UpdatePerUserSystemParameters
    }

    # Check Windows version
    $windowsVersion = (Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion").CurrentBuild

    # Apply appropriate wallpaper based on Windows version or existence of dark mode wallpaper
    if ($windowsVersion -ge 22000) {
        # Assuming Windows 11 starts at build 22000
        if (Test-Path $darkModeWallpaperPath) {
            Set-Wallpaper -wallpaperPath $darkModeWallpaperPath
        }
    }
    else {
        # Apply default wallpaper for Windows 10
        Set-Wallpaper -wallpaperPath $defaultWallpaperPath
    }

    $MultilineComment = @"
Windows Registry Editor Version 5.00

; EASE OF ACCESS
; disable narrator
[HKEY_CURRENT_USER\Software\Microsoft\Narrator\NoRoam]
"DuckAudio"=dword:********
"WinEnterLaunchEnabled"=dword:********
"ScriptingEnabled"=dword:********
"OnlineServicesEnabled"=dword:********
"EchoToggleKeys"=dword:********

; disable narrator settings
[HKEY_CURRENT_USER\Software\Microsoft\Narrator]
"NarratorCursorHighlight"=dword:********
"CoupleNarratorCursorKeyboard"=dword:********
"IntonationPause"=dword:********
"ReadHints"=dword:********
"ErrorNotificationType"=dword:********
"EchoChars"=dword:********
"EchoWords"=dword:********

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Narrator\NarratorHome]
"MinimizeType"=dword:********
"AutoStart"=dword:********

; disable ease of access settings 
[HKEY_CURRENT_USER\Software\Microsoft\Ease of Access]
"selfvoice"=dword:********
"selfscan"=dword:********

[HKEY_CURRENT_USER\Control Panel\Accessibility]
"Sound on Activation"=dword:********
"Warning Sounds"=dword:********

[HKEY_CURRENT_USER\Control Panel\Accessibility\HighContrast]
"Flags"="4194"

[HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response]
"Flags"="2"
"AutoRepeatRate"="0"
"AutoRepeatDelay"="0"

[HKEY_CURRENT_USER\Control Panel\Accessibility\MouseKeys]
"Flags"="130"
"MaximumSpeed"="39"
"TimeToMaximumSpeed"="3000"

[HKEY_CURRENT_USER\Control Panel\Accessibility\StickyKeys]
"Flags"="2"

[HKEY_CURRENT_USER\Control Panel\Accessibility\ToggleKeys]
"Flags"="34"

[HKEY_CURRENT_USER\Control Panel\Accessibility\SoundSentry]
"Flags"="0"
"FSTextEffect"="0"
"TextEffect"="0"
"WindowsEffect"="0"

[HKEY_CURRENT_USER\Control Panel\Accessibility\SlateLaunch]
"ATapp"=""
"LaunchAT"=dword:********

; CLOCK AND REGION
; disable notify me when the clock changes
[HKEY_CURRENT_USER\Control Panel\TimeDate]
"DstNotification"=dword:********

; APPEARANCE AND PERSONALIZATION
; open file explorer to this pc
; show file name extensions
; disable display file size information in folder tips
; disable show pop-up description for folder and desktop items
; disable show preview handlers in preview pane
; disable show status bar
; disable show sync provider notifications
; disable use sharing wizard
; disable animations in the taskbar
; enable show thumbnails instead of icons
; disable show translucent selection rectangle
; disable use drop shadows for icon labels on the desktop
; more pins personalization start
; disable show account-related notifications
; disable show recently opened items in start, jump lists and file explorer
; left taskbar alignment
; remove chat from taskbar
; remove task view from taskbar
; remove copilot from taskbar
; disable show recommendations for tips shortcuts new apps and more
; disable share any window from my taskbar
; disable snap window settings - SnapAssist to JointResize Entries
; alt tab open windows only
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced]
"LaunchTo"=dword:********
"HideFileExt"=dword:********
"FolderContentsInfoTip"=dword:********
"ShowInfoTip"=dword:********
"ShowPreviewHandlers"=dword:********
"ShowStatusBar"=dword:********
"ShowSyncProviderNotifications"=dword:********
"SharingWizardOn"=dword:********
"TaskbarAnimations"=dword:0
"IconsOnly"=dword:0
"ListviewAlphaSelect"=dword:0
"ListviewShadow"=dword:0
"Start_Layout"=dword:********
"Start_AccountNotifications"=dword:********
"Start_TrackDocs"=dword:******** 
"TaskbarAl"=dword:********
"TaskbarMn"=dword:********
"ShowTaskViewButton"=dword:********
"ShowCopilotButton"=dword:********
"Start_IrisRecommendations"=dword:********
"TaskbarSn"=dword:********
"SnapAssist"=dword:********
"DITest"=dword:********
"EnableSnapBar"=dword:********
"EnableTaskGroups"=dword:********
"EnableSnapAssistFlyout"=dword:********
"SnapFill"=dword:********
"JointResize"=dword:********
"MultiTaskingAltTabFilter"=dword:********

; hide frequent folders in quick access
; disable show files from office.com
; show all taskbar icons on Windows 10
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer]
"ShowFrequent"=dword:********
"ShowCloudFilesInQuickAccess"=dword:********
"EnableAutoTray"=dword:********

; enable display full path in the title bar
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\CabinetState]
"FullPath"=dword:********

; HARDWARE AND SOUND
; sound communications do nothing
[HKEY_CURRENT_USER\Software\Microsoft\Multimedia\Audio]
"UserDuckingPreference"=dword:********

; disable enhance pointer precision
; mouse fix (no accel with epp on)
[HKEY_CURRENT_USER\Control Panel\Mouse]
"MouseSpeed"="0"
"MouseThreshold1"="0"
"MouseThreshold2"="0"
"MouseSensitivity"="10"
"SmoothMouseXCurve"=hex:\
	00,00,00,00,00,00,00,00,\
	C0,CC,0C,00,00,00,00,00,\
	80,99,19,00,00,00,00,00,\
	40,66,26,00,00,00,00,00,\
	00,33,33,00,00,00,00,00
"SmoothMouseYCurve"=hex:\
	00,00,00,00,00,00,00,00,\
	00,00,38,00,00,00,00,00,\
	00,00,70,00,00,00,00,00,\
	00,00,A8,00,00,00,00,00,\
	00,00,E0,00,00,00,00,00

; SYSTEM AND SECURITY
; set appearance options to custom
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects]
"VisualFXSetting"=dword:3

; disable animate controls and elements inside windows
; disable fade or slide menus into view
; disable fade or slide tooltips into view
; disable fade out menu items after clicking
; disable show shadows under mouse pointer
; disable show shadows under windows
; disable slide open combo boxes
; disable smooth-scroll list boxes
; enable smooth edges of screen fonts
; 100% dpi scaling
; disable fix scaling for apps
; disable menu show delay
[HKEY_CURRENT_USER\Control Panel\Desktop]
"UserPreferencesMask"=hex(2):90,12,03,80,10,00,00,00
"FontSmoothing"="2"
"LogPixels"=dword:********
"Win8DpiScaling"=dword:********
"EnablePerProcessSystemDPI"=dword:********
"MenuShowDelay"="0"

; --IMMERSIVE CONTROL PANEL--
; PRIVACY
; disable show me notification in the settings app
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\SystemSettings\AccountNotifications]
"EnableAccountNotifications"=dword:********

; disable voice activation
[HKEY_CURRENT_USER\Software\Microsoft\Speech_OneCore\Settings\VoiceActivation\UserPreferenceForAllApps]
"AgentActivationEnabled"=dword:********

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Speech_OneCore\Settings\VoiceActivation\UserPreferenceForAllApps]
"AgentActivationLastUsed"=dword:********

; disable other devices 
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\CapabilityAccessManager\ConsentStore\bluetoothSync]
"Value"="Deny"

; disable let websites show me locally relevant content by accessing my language list 
[HKEY_CURRENT_USER\Control Panel\International\User Profile]
"HttpAcceptLanguageOptOut"=dword:********

; disable let windows improve start and search results by tracking app launches  
[HKEY_CURRENT_USER\Software\Policies\Microsoft\Windows\EdgeUI]
"DisableMFUTracking"=dword:********

; disable personal inking and typing dictionary
[HKEY_CURRENT_USER\Software\Microsoft\InputPersonalization]
"RestrictImplicitInkCollection"=dword:********
"RestrictImplicitTextCollection"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\InputPersonalization\TrainedDataStore]
"HarvestContacts"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Personalization\Settings]
"AcceptedPrivacyPolicy"=dword:********

; feedback frequency never
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Siuf\Rules]
"NumberOfSIUFInPeriod"=dword:********
"PeriodInNanoSeconds"=-

; SEARCH
; disable search highlights
; disable search history
; disable safe search
; disable cloud content search for work or school account
; disable cloud content search for microsoft account
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\SearchSettings]
"IsDynamicSearchBoxEnabled"=dword:********
"IsDeviceSearchHistoryEnabled"=dword:********
"SafeSearchMode"=dword:********
"IsAADCloudSearchEnabled"=dword:********
"IsMSACloudSearchEnabled"=dword:********

; EASE OF ACCESS
; disable magnifier settings 
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\ScreenMagnifier]
"FollowCaret"=dword:********
"FollowNarrator"=dword:********
"FollowMouse"=dword:********
"FollowFocus"=dword:********

; GAMING
; disable game bar
[HKEY_CURRENT_USER\System\GameConfigStore]
"GameDVR_Enabled"=dword:********

; disable enable open xbox game bar using game controller
; enable game mode
[HKEY_CURRENT_USER\Software\Microsoft\GameBar]
"UseNexusForGameBarEnabled"=dword:********
"AutoGameModeEnabled"=dword:********

; other settings
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\GameDVR]
"AppCaptureEnabled"=dword:********
"AudioEncodingBitrate"=dword:0001f400
"AudioCaptureEnabled"=dword:********
"CustomVideoEncodingBitrate"=dword:003d0900
"CustomVideoEncodingHeight"=dword:000002d0
"CustomVideoEncodingWidth"=dword:00000500
"HistoricalBufferLength"=dword:0000001e
"HistoricalBufferLengthUnit"=dword:********
"HistoricalCaptureEnabled"=dword:********
"HistoricalCaptureOnBatteryAllowed"=dword:********
"HistoricalCaptureOnWirelessDisplayAllowed"=dword:********
"MaximumRecordLength"=hex(b):00,D0,88,C3,10,00,00,00
"VideoEncodingBitrateMode"=dword:********
"VideoEncodingResolutionMode"=dword:********
"VideoEncodingFrameRateMode"=dword:********
"EchoCancellationEnabled"=dword:********
"CursorCaptureEnabled"=dword:********
"VKToggleGameBar"=dword:********
"VKMToggleGameBar"=dword:********
"VKSaveHistoricalVideo"=dword:********
"VKMSaveHistoricalVideo"=dword:********
"VKToggleRecording"=dword:********
"VKMToggleRecording"=dword:********
"VKTakeScreenshot"=dword:********
"VKMTakeScreenshot"=dword:********
"VKToggleRecordingIndicator"=dword:********
"VKMToggleRecordingIndicator"=dword:********
"VKToggleMicrophoneCapture"=dword:********
"VKMToggleMicrophoneCapture"=dword:********
"VKToggleCameraCapture"=dword:********
"VKMToggleCameraCapture"=dword:********
"VKToggleBroadcast"=dword:********
"VKMToggleBroadcast"=dword:********
"MicrophoneCaptureEnabled"=dword:********
"SystemAudioGain"=hex(b):10,27,00,00,00,00,00,00
"MicrophoneGain"=hex(b):10,27,00,00,00,00,00,00

; TIME & LANGUAGE 
; disable show the voice typing mic button
; disable typing insights
[HKEY_CURRENT_USER\Software\Microsoft\input\Settings]
"IsVoiceTypingKeyEnabled"=dword:********
"InsightsEnabled"=dword:********

; disable capitalize the first letter of each sentence
; disable play key sounds as i type
; disable add a period after i double-tap the spacebar
; disable show key background
[HKEY_CURRENT_USER\Software\Microsoft\TabletTip\1.7]
"EnableAutoShiftEngage"=dword:********
"EnableKeyAudioFeedback"=dword:********
"EnableDoubleTapSpace"=dword:********
"IsKeyBackgroundEnabled"=dword:********

; PERSONALIZATION
; dark theme 
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize]
"AppsUseLightTheme"=dword:********
"SystemUsesLightTheme"=dword:********
"EnableTransparency"=dword:********

; disable web search in start menu 
[HKEY_CURRENT_USER\SOFTWARE\Policies\Microsoft\Windows\Explorer]
"DisableSearchBoxSuggestions"=dword:********

; Remove meet now
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer]
"NoStartMenuMFUprogramsList"=-
"NoInstrumentation"=-
"HideSCAMeetNow"=dword:********

; remove search from taskbar
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Search]
"SearchboxTaskbarMode"=dword:********

; disable use dynamic lighting on my devices
; disable compatible apps in the forground always control lighting
; disable match my windows accent color
[HKEY_CURRENT_USER\Software\Microsoft\Lighting]
"AmbientLightingEnabled"=dword:********
"ControlledByForegroundApp"=dword:********
"UseSystemAccentColor"=dword:********

; DEVICES
; disable let windows manage my default printer
[HKEY_CURRENT_USER\Software\Microsoft\Windows NT\CurrentVersion\Windows]
"LegacyDefaultPrinterMode"=dword:********

; disable write with your fingertip
[HKEY_CURRENT_USER\Software\Microsoft\TabletTip\EmbeddedInkControl]
"EnableInkingWithTouch"=dword:********

; SYSTEM
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\DWM]
"UseDpiScaling"=dword:********

; disable variable refresh rate & enable optimizations for windowed games
[HKEY_CURRENT_USER\Software\Microsoft\DirectX\UserGpuPreferences]
"DirectXUserGlobalSettings"="SwapEffectUpgradeEnable=1;VRROptimizeEnable=0;"

; disable notifications
; Disable Notifications on Lock Screen
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\PushNotifications]
"ToastEnabled"=dword:********
"LockScreenToastEnabled"=dword:********

; Disable Allow Notifications to Play Sounds
; Disable Notifications on Lock Screen
; Disable Show Reminders and VoIP Calls Notifications on Lock Screen
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings]
"NOC_GLOBAL_SETTING_ALLOW_NOTIFICATION_SOUND"=dword:********
"NOC_GLOBAL_SETTING_ALLOW_TOASTS_ABOVE_LOCK"=dword:********
"NOC_GLOBAL_SETTING_ALLOW_CRITICAL_TOASTS_ABOVE_LOCK"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings\Windows.SystemToast.SecurityAndMaintenance]
"Enabled"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings\windows.immersivecontrolpanel_cw5n1h2txyewy!microsoft.windows.immersivecontrolpanel]
"Enabled"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings\Windows.SystemToast.CapabilityAccess]
"Enabled"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings\Windows.SystemToast.StartupApp]
"Enabled"=dword:********

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\UserProfileEngagement]
"ScoobeSystemSettingEnabled"=dword:********

; disable suggested actions
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\SmartActionPlatform\SmartClipboard]
"Disabled"=dword:********

; battery options optimize for video quality
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\VideoSettings]
"VideoQualityOnBattery"=dword:********

; UWP Apps
; disable windows input experience preload
[HKEY_CURRENT_USER\Software\Microsoft\input]
"IsInputAppPreloadEnabled"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Dsh]
"IsPrelaunchEnabled"=dword:********

; disable copilot
[HKEY_CURRENT_USER\Software\Policies\Microsoft\Windows\WindowsCopilot]
"TurnOffWindowsCopilot"=dword:********

; DISABLE ADVERTISING & PROMOTIONAL
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager]
"ContentDeliveryAllowed"=dword:********
"FeatureManagementEnabled"=dword:********
"OemPreInstalledAppsEnabled"=dword:********
"PreInstalledAppsEnabled"=dword:********
"PreInstalledAppsEverEnabled"=dword:********
"RotatingLockScreenEnabled"=dword:********
"RotatingLockScreenOverlayEnabled"=dword:********
"SilentInstalledAppsEnabled"=dword:********
"SlideshowEnabled"=dword:********
"SoftLandingEnabled"=dword:********
"SubscribedContent-310093Enabled"=dword:********
"SubscribedContent-314563Enabled"=dword:********
"SubscribedContent-338388Enabled"=dword:********
"SubscribedContent-338389Enabled"=dword:********
"SubscribedContent-338393Enabled"=dword:********
"SubscribedContent-353694Enabled"=dword:********
"SubscribedContent-353696Enabled"=dword:********
"SubscribedContent-353698Enabled"=dword:********
"SubscribedContentEnabled"=dword:********
"SystemPaneSuggestionsEnabled"=dword:********

; OTHER
; remove gallery
[HKEY_CURRENT_USER\Software\Classes\CLSID\{e88865ea-0e1c-4e20-9aa6-edcd0212c87c}]
"System.IsPinnedToNameSpaceTree"=dword:********

; restore the classic context menu
[HKEY_CURRENT_USER\Software\Classes\CLSID\{86ca1aa0-34aa-4e8b-a509-50c905bae2a2}\InprocServer32]
@=""

; removes OneDrive Setup
[-HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Run]
"OneDriveSetup"=-

; Hides the Try New Outlook Button
[HKEY_CURRENT_USER\Software\Microsoft\Office\16.0\Outlook\Options\General]
"HideNewOutlookToggle"=dword:********
"@
    Set-Content -Path "$env:TEMP\Optimize_User_Registry.reg" -Value $MultilineComment -Force
    Regedit.exe /S "$env:TEMP\Optimize_User_Registry.reg"
    Show-Header
    Write-Host "Recommended User Registry Settings Applied." -ForegroundColor Green
    Wait-IfNotSpecialize
}

function Set-DefaultHKCURegistry {
    Clear-Host
    Write-Host "Restoring User Default Registry Settings . . ."
    $MultilineComment = @"
Windows Registry Editor Version 5.00

; --LEGACY CONTROL PANEL--
; EASE OF ACCESS
; narrator
[HKEY_CURRENT_USER\Software\Microsoft\Narrator\NoRoam]
"DuckAudio"=-
"WinEnterLaunchEnabled"=-
"ScriptingEnabled"=-
"OnlineServicesEnabled"=-
"EchoToggleKeys"=-

; narrator settings
[HKEY_CURRENT_USER\Software\Microsoft\Narrator]
"NarratorCursorHighlight"=-
"CoupleNarratorCursorKeyboard"=-
"IntonationPause"=-
"ReadHints"=-
"ErrorNotificationType"=-
"EchoChars"=-
"EchoWords"=-

[-HKEY_CURRENT_USER\SOFTWARE\Microsoft\Narrator\NarratorHome]

; ease of access settings
[-HKEY_CURRENT_USER\Software\Microsoft\Ease of Access]

[HKEY_CURRENT_USER\Control Panel\Accessibility]
"Sound on Activation"=-
"Warning Sounds"=-

[HKEY_CURRENT_USER\Control Panel\Accessibility\HighContrast]
"Flags"="126"

[HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response]
"Flags"="126"
"AutoRepeatRate"="500"
"AutoRepeatDelay"="1000"

[HKEY_CURRENT_USER\Control Panel\Accessibility\MouseKeys]
"Flags"="62"
"MaximumSpeed"="80"
"TimeToMaximumSpeed"="3000"

[HKEY_CURRENT_USER\Control Panel\Accessibility\StickyKeys]
"Flags"="510"

[HKEY_CURRENT_USER\Control Panel\Accessibility\ToggleKeys]
"Flags"="62"

[HKEY_CURRENT_USER\Control Panel\Accessibility\SoundSentry]
"Flags"="2"
"FSTextEffect"="0"
"TextEffect"="0"
"WindowsEffect"="1"

[HKEY_CURRENT_USER\Control Panel\Accessibility\SlateLaunch]
"ATapp"="narrator"
"LaunchAT"=dword:********

; CLOCK AND REGION
; notify me when the clock changes
[-HKEY_CURRENT_USER\Control Panel\TimeDate]

; APPEARANCE AND PERSONALIZATION
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced]
"LaunchTo"=-
"HideFileExt"=dword:********
"FolderContentsInfoTip"=-
"ShowInfoTip"=dword:********
"ShowPreviewHandlers"=-
"ShowStatusBar"=dword:********
"ShowSyncProviderNotifications"=-
"SharingWizardOn"=-
"TaskbarAnimations"=dword:1
"IconsOnly"=dword:0
"ListviewAlphaSelect"=dword:1
"ListviewShadow"=dword:1
"Start_Layout"=-
"Start_AccountNotifications"=-
"Start_TrackDocs"=-
"TaskbarAl"=-
"TaskbarMn"=-
"ShowTaskViewButton"=-
"ShowCopilotButton"=-
"Start_IrisRecommendations"=-
"TaskbarSn"=-
"SnapAssist"=-
"DITest"=-
"EnableSnapBar"=-
"EnableTaskGroups"=-
"EnableSnapAssistFlyout"=-
"SnapFill"=-
"JointResize"=-
"MultiTaskingAltTabFilter"=-

; frequent folders in quick access
; show files from office.com
; don't show all taskbar icons
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer]
"ShowFrequent"=-
"ShowCloudFilesInQuickAccess"=-
"EnableAutoTray"=-

; display full path in the title bar
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\CabinetState]
"FullPath"=dword:********

; HARDWARE AND SOUND
; sound communications
[HKEY_CURRENT_USER\Software\Microsoft\Multimedia\Audio]
"UserDuckingPreference"=-

; enhance pointer precision
; mouse (default accel with epp on)
[HKEY_CURRENT_USER\Control Panel\Mouse]
"MouseSpeed"="1"
"MouseThreshold1"="6"
"MouseThreshold2"="10"
"MouseSensitivity"="10"
"SmoothMouseXCurve"=hex:00,00,00,00,00,00,00,00,15,6e,00,00,00,00,00,00,00,40,\
  01,00,00,00,00,00,29,dc,03,00,00,00,00,00,00,00,28,00,00,00,00,00
"SmoothMouseYCurve"=hex:00,00,00,00,00,00,00,00,fd,11,01,00,00,00,00,00,00,24,\
  04,00,00,00,00,00,00,fc,12,00,00,00,00,00,00,c0,bb,01,00,00,00,00

; SYSTEM AND SECURITY
; set appearance options
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects]
"VisualFXSetting"=-

; animate controls and elements inside windows
; fade or slide menus into view
; fade or slide tooltips into view
; fade out menu items after clicking
; show shadows under mouse pointer
; show shadows under windows
; slide open combo boxes
; smooth-scroll list boxes
; smooth edges of screen fonts
; dpi scaling
; fix scaling for apps
; menu show delay
[HKEY_CURRENT_USER\Control Panel\Desktop]
"UserPreferencesMask"=hex(2):9e,1e,07,80,12,00,00,00
"FontSmoothing"="2"
"LogPixels"=-
"Win8DpiScaling"=dword:********
"EnablePerProcessSystemDPI"=-
"MenuShowDelay"="400"

; --IMMERSIVE CONTROL PANEL--
; PRIVACY
; show me notification in the settings app
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\SystemSettings\AccountNotifications]
"EnableAccountNotifications"=-

; allow location override
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\CPSS\Store\UserLocationOverridePrivacySetting]
"Value"=dword:********

; voice activation
[-HKEY_CURRENT_USER\Software\Microsoft\Speech_OneCore\Settings]

; other devices 
[-HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\CapabilityAccessManager\ConsentStore\bluetoothSync]

; let websites show me locally relevant content by accessing my language list 
[HKEY_CURRENT_USER\Control Panel\International\User Profile]
"HttpAcceptLanguageOptOut"=-

; let windows improve start and search results by tracking app launches  
[-HKEY_CURRENT_USER\Software\Policies\Microsoft\Windows\EdgeUI]

; personal inking and typing dictionary
[HKEY_CURRENT_USER\Software\Microsoft\InputPersonalization]
"RestrictImplicitInkCollection"=dword:********
"RestrictImplicitTextCollection"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\InputPersonalization\TrainedDataStore]
"HarvestContacts"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Personalization\Settings]
"AcceptedPrivacyPolicy"=dword:********

; feedback frequency
[-HKEY_CURRENT_USER\SOFTWARE\Microsoft\Siuf]

; SEARCH
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\SearchSettings]
"IsDynamicSearchBoxEnabled"=-
"IsDeviceSearchHistoryEnabled"=-
"SafeSearchMode"=-
"IsAADCloudSearchEnabled"=-
"IsMSACloudSearchEnabled"=-

; EASE OF ACCESS
; magnifier settings 
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\ScreenMagnifier]
"FollowCaret"=-
"FollowNarrator"=-
"FollowMouse"=-
"FollowFocus"=-

; GAMING
; game bar
[HKEY_CURRENT_USER\System\GameConfigStore]
"GameDVR_Enabled"=dword:********

; enable open xbox game bar using game controller
; game mode
[HKEY_CURRENT_USER\Software\Microsoft\GameBar]
"UseNexusForGameBarEnabled"=-
"AutoGameModeEnabled"=-

; other settings
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\GameDVR]
"AppCaptureEnabled"=-
"AudioEncodingBitrate"=-
"AudioCaptureEnabled"=-
"CustomVideoEncodingBitrate"=-
"CustomVideoEncodingHeight"=-
"CustomVideoEncodingWidth"=-
"HistoricalBufferLength"=-
"HistoricalBufferLengthUnit"=-
"HistoricalCaptureEnabled"=-
"HistoricalCaptureOnBatteryAllowed"=-
"HistoricalCaptureOnWirelessDisplayAllowed"=-
"MaximumRecordLength"=-
"VideoEncodingBitrateMode"=-
"VideoEncodingResolutionMode"=-
"VideoEncodingFrameRateMode"=-
"EchoCancellationEnabled"=-
"CursorCaptureEnabled"=-
"VKToggleGameBar"=-
"VKMToggleGameBar"=-
"VKSaveHistoricalVideo"=-
"VKMSaveHistoricalVideo"=-
"VKToggleRecording"=-
"VKMToggleRecording"=-
"VKTakeScreenshot"=-
"VKMTakeScreenshot"=-
"VKToggleRecordingIndicator"=-
"VKMToggleRecordingIndicator"=-
"VKToggleMicrophoneCapture"=-
"VKMToggleMicrophoneCapture"=-
"VKToggleCameraCapture"=-
"VKMToggleCameraCapture"=-
"VKToggleBroadcast"=-
"VKMToggleBroadcast"=-
"MicrophoneCaptureEnabled"=-
"SystemAudioGain"=-
"MicrophoneGain"=-

; TIME & LANGUAGE 
; show the voice typing mic button
; typing insights
[HKEY_CURRENT_USER\Software\Microsoft\input\Settings]
"IsVoiceTypingKeyEnabled"=-
"InsightsEnabled"=-

; capitalize the first letter of each sentence
; play key sounds as i type
; add a period after i double-tap the spacebar
; show key background
[HKEY_CURRENT_USER\Software\Microsoft\TabletTip\1.7]
"EnableAutoShiftEngage"=-
"EnableKeyAudioFeedback"=-
"EnableDoubleTapSpace"=-
"IsKeyBackgroundEnabled"=-

; PERSONALIZATION
; light theme 
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize]
"AppsUseLightTheme"=dword:********
"SystemUsesLightTheme"=dword:********

[-HKEY_CURRENT_USER\SOFTWARE\Policies\Microsoft\Windows\Explorer]

[-HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer]

; search from taskbar
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Search]
"SearchboxTaskbarMode"=-

; meet now
[-HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer]

; use dynamic lighting on my devices
; compatible apps in the forground always control lighting
; match my windows accent color
[HKEY_CURRENT_USER\Software\Microsoft\Lighting]
"AmbientLightingEnabled"=dword:********
"ControlledByForegroundApp"=-
"UseSystemAccentColor"=dword:********

; DEVICES
; let windows manage my default printer
[HKEY_CURRENT_USER\Software\Microsoft\Windows NT\CurrentVersion\Windows]
"LegacyDefaultPrinterMode"=dword:ffffffff

; write with your fingertip
[-HKEY_CURRENT_USER\Software\Microsoft\TabletTip\EmbeddedInkControl]

; SYSTEM
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\DWM]
"UseDpiScaling"=-

; variable refresh rate & optimizations for windowed games
[HKEY_CURRENT_USER\Software\Microsoft\DirectX\UserGpuPreferences]
"DirectXUserGlobalSettings"=-

; Notification defaults
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\PushNotifications]
"ToastEnabled"=-
"LockScreenToastEnabled"=-

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings]
"NOC_GLOBAL_SETTING_ALLOW_NOTIFICATION_SOUND"=-
"NOC_GLOBAL_SETTING_ALLOW_TOASTS_ABOVE_LOCK"=-
"NOC_GLOBAL_SETTING_ALLOW_CRITICAL_TOASTS_ABOVE_LOCK"=-

[-HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings\Windows.SystemToast.SecurityAndMaintenance]

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings\windows.immersivecontrolpanel_cw5n1h2txyewy!microsoft.windows.immersivecontrolpanel]
"Enabled"=-

[-HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings\Windows.SystemToast.CapabilityAccess]

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings\Windows.SystemToast.StartupApp]
"Enabled"=dword:********

[-HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\UserProfileEngagement]

; suggested actions
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\SmartActionPlatform\SmartClipboard]
"Disabled"=-

; battery options optimize
[-HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\VideoSettings]

; UWP APPS
; disable windows input experience preload
[HKEY_CURRENT_USER\Software\Microsoft\input]
"IsInputAppPreloadEnabled"=-

[-HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Dsh]

; copilot
[-HKEY_CURRENT_USER\Software\Policies\Microsoft\Windows\WindowsCopilot]

; ADVERTISING & PROMOTIONAL
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager]
"ContentDeliveryAllowed"=dword:********
"FeatureManagementEnabled"=dword:********
"OemPreInstalledAppsEnabled"=dword:********
"PreInstalledAppsEnabled"=dword:********
"PreInstalledAppsEverEnabled"=dword:********
"RotatingLockScreenEnabled"=dword:********
"RotatingLockScreenOverlayEnabled"=dword:********
"SilentInstalledAppsEnabled"=dword:********
"SlideshowEnabled"=dword:********
"SoftLandingEnabled"=dword:********
"SubscribedContent-310093Enabled"=-
"SubscribedContent-314563Enabled"=-
"SubscribedContent-338388Enabled"=-
"SubscribedContent-338389Enabled"=-
"SubscribedContent-338393Enabled"=-
"SubscribedContent-353694Enabled"=-
"SubscribedContent-353696Enabled"=-
"SubscribedContent-353698Enabled"=-
"SubscribedContentEnabled"=dword:********
"SystemPaneSuggestionsEnabled"=dword:********

; OTHER
; gallery
[-HKEY_CURRENT_USER\Software\Classes\CLSID\{e88865ea-0e1c-4e20-9aa6-edcd0212c87c}]

; context menu
[-HKEY_CURRENT_USER\Software\Classes\CLSID\{86ca1aa0-34aa-4e8b-a509-50c905bae2a2}]
"@
    Set-Content -Path "$env:TEMP\Restore_User_Registry.reg" -Value $MultilineComment -Force
    Regedit.exe /S "$env:TEMP\Restore_User_Registry.reg"
    Show-Header
    Write-Host "Default User Registry Settings Applied." -ForegroundColor Green
    Wait-IfNotSpecialize
}
# End of Registry Optimizations

# Start of Tasks and Services Functions
function Set-ServiceStartup {
    # List of services to set to Disabled
    $disabledServices = @(
    'AJRouter', 'AppVClient', 'AssignedAccessManagerSvc', 
    'DiagTrack', 'DialogBlockingService', 'NetTcpPortSharing',
    'RemoteAccess', 'RemoteRegistry', 'shpamsvc', 
    'ssh-agent', 'tzautoupdate', 'uhssvc',
    'UevAgentService'
	)

    # List of services to set to Manual
    $manualServices = @(
    'ALG', 'AppIDSvc', 'AppMgmt', 'AppReadiness', 'AppXSvc', 'Appinfo',
    'AxInstSV', 'BDESVC', 'BITS', 'BTAGService', 'BcastDVRUserService_*',
    'Browser', 'CDPSvc', 'CDPUserSvc_*', 'COMSysApp', 'CaptureService_*',
    'CertPropSvc', 'ClipSVC', 'ConsentUxUserSvc_*', 'CscService', 'DcpSvc',
    'DevQueryBroker', 'DeviceAssociationBrokerSvc_*', 'DeviceAssociationService', 
    'DeviceInstall', 'DevicePickerUserSvc_*', 'DevicesFlowUserSvc_*', 
    'DisplayEnhancementService', 'DmEnrollmentSvc', 'DoSvc', 'DsSvc', 'DsmSvc',
    'EFS', 'EapHost', 'EntAppSvc', 'FDResPub', 'Fax', 'FrameServer',
    'FrameServerMonitor', 'GraphicsPerfSvc', 'HomeGroupListener', 
    'HomeGroupProvider', 'HvHost', 'IEEtwCollectorService', 'IKEEXT',
    'InstallService', 'InventorySvc', 'IpxlatCfgSvc', 'KtmRm', 'LicenseManager',
    'LxpSvc', 'MSDTC', 'MSiSCSI', 'MapsBroker', 'McpManagementService', 
    'MessagingService_*', 'MicrosoftEdgeElevationService', 
    'MixedRealityOpenXRSvc', 'MsKeyboardFilter', 'NPSMSvc_*', 'NaturalAuthentication',
    'NcaSvc', 'NcbService', 'NcdAutoSetup', 'Netman', 'NgcCtnrSvc', 'NgcSvc',
    'NlaSvc', 'P9RdrService_*', 'PNRPAutoReg', 'PNRPsvc', 'PcaSvc', 'PeerDistSvc',
    'PenService_*', 'PerfHost', 'PhoneSvc', 'PimIndexMaintenanceSvc_*', 'PlugPlay',
    'PolicyAgent', 'PrintNotify', 'PrintWorkflowUserSvc_*', 'PushToInstall', 'QWAVE',
    'RasAuto', 'RasMan', 'RetailDemo', 'RmSvc', 'RpcLocator', 'SCPolicySvc',
    'SCardSvr', 'SDRSVC', 'SEMgrSvc', 'SecurityHealthService', 
    'SensorDataService', 'SensorService', 'SensrSvc', 'SessionEnv', 
    'SharedAccess', 'SharedRealitySvc', 'SmsRouter', 'SstpSvc', 
    'StateRepository', 'StiSvc', 'StorSvc', 'TabletInputService', 'TapiSrv',
    'TextInputManagementService', 'TieringEngineService', 'TimeBroker',
    'TimeBrokerSvc', 'TokenBroker', 'TroubleshootingSvc', 'TrustedInstaller',
    'UI0Detect', 'UdkUserSvc_*', 'UmRdpService', 'UnistoreSvc_*', 
    'UserDataSvc_*', 'UsoSvc', 'VSS', 'VacSvc', 'W32Time', 'WEPHOSTSVC',
    'WFDSConMgrSvc', 'WMPNetworkSvc', 'WManSvc', 'WPDBusEnum', 'WSService',
    'WSearch', 'WaaSMedicSvc', 'WalletService', 'WarpJITSvc', 'WbioSrvc',
    'WcsPlugInService', 'WdiServiceHost', 'WdiSystemHost', 'WebClient', 'Wecsvc',
    'WerSvc', 'WiaRpc', 'WinHttpAutoProxySvc', 'WinRM', 'WpcMonSvc', 
    'WpnService', 'WwanSvc', 'XblAuthManager', 'XblGameSave', 'XboxGipSvc', 
    'XboxNetApiSvc', 'autotimesvc', 'bthserv', 'camsvc', 'cbdhsvc_*',
    'cloudidsvc', 'dcsvc', 'defragsvc', 'diagnosticshub.standardcollector.service',
    'diagsvc', 'dmwappushservice', 'dot3svc', 'edgeupdate', 'edgeupdatem', 
    'embeddedmode', 'fdPHost', 'fhsvc', 'hidserv', 'icssvc', 'lfsvc', 
    'lltdsvc', 'lmhosts', 'msiserver', 'netprofm', 'p2pimsvc', 'p2psvc', 
    'perceptionsimulation', 'pla', 'seclogon', 'smphost', 'spectrum', 
    'sppsvc', 'svsvc', 'swprv', 'upnphost', 'vds', 'vm3dservice', 
    'vmicguestinterface', 'vmicheartbeat', 'vmickvpexchange', 'vmicrdv', 
    'vmicshutdown', 'vmictimesync', 'vmicvmsession', 'vmicvss', 'wbengine', 
    'wcncsvc', 'webthreatdefsvc', 'wercplsupport', 'wisvc', 'wlidsvc', 
    'wlpasvc', 'wmiApSrv', 'workfolderssvc', 'wuauserv', 'wudfsvc'
    )

    # Set the services in the disabledServices list to Disabled
    foreach ($service in $disabledServices) {
        try {
            Set-Service -Name $service -StartupType Disabled -ErrorAction SilentlyContinue | Out-Null
        }
        catch {
            Show-Header
            Write-Host "Failed to set $service to Disabled: $_" -ForegroundColor Yellow
            Wait-IfNotSpecialize
        }
    }

    # Set the services in the manualServices list to Manual
    foreach ($service in $manualServices) {
        try {
            Set-Service -Name $service -StartupType Manual -ErrorAction SilentlyContinue | Out-Null
        }
        catch {
            Show-Header
            Write-Host "Failed to set $service to Manual: $_" -ForegroundColor Yellow
            Wait-IfNotSpecialize
        }
    }

    Show-Header
    Write-Host "Service startup types updated successfully." -ForegroundColor Green
    Wait-IfNotSpecialize
}

function Set-DefaultServices {
    # Get all services that are not currently set to Automatic and revert them
    $allServices = Get-Service | Where-Object { $_.StartType -ne 'Automatic' }

    $successCount = 0
    foreach ($service in $allServices) {
        try {
            Show-Header
            Write-Host "Setting services to Automatic where permissions are allowed. Please wait . . ."
            # Set the service startup type to Automatic using Set-Service
            Set-Service -Name $service.Name -StartupType Automatic 2>&1 | Out-Null

            # Forcibly set the startup type to Automatic using WMI as a fallback
            $wmiService = Get-WmiObject -Class Win32_Service -Filter "Name='$($service.Name)'" 2>&1 | Out-Null
            if ($wmiService) {
                $result = $wmiService.ChangeStartMode("Automatic") 2>&1 | Out-Null
                if ($result.ReturnValue -eq 0) {
                    $successCount++
                }
            }
        }
        catch {
            # Silently continue if a service fails
            continue
        }
    }
    Show-Header
    Write-Host "Successfully set services to Automatic where permissions allowed." -ForegroundColor Green
    Wait-IfNotSpecialize
}

function Disable-ScheduledTasks {
    # Define the list of scheduled tasks to disable
    $scheduledTasks = @(
        "Microsoft\Windows\Application Experience\Microsoft Compatibility Appraiser",
        "Microsoft\Windows\Application Experience\ProgramDataUpdater",
        "Microsoft\Windows\Autochk\Proxy",
        "Microsoft\Windows\Customer Experience Improvement Program\Consolidator",
        "Microsoft\Windows\Customer Experience Improvement Program\UsbCeip",
        "Microsoft\Windows\DiskDiagnostic\Microsoft-Windows-DiskDiagnosticDataCollector",
        "Microsoft\Windows\Feedback\Siuf\DmClient",
        "Microsoft\Windows\Feedback\Siuf\DmClientOnScenarioDownload",
        "Microsoft\Windows\Windows Error Reporting\QueueReporting",
        "Microsoft\Windows\Application Experience\MareBackup",
        "Microsoft\Windows\Application Experience\StartupAppTask",
        "Microsoft\Windows\Application Experience\PcaPatchDbTask",
        "Microsoft\Windows\Maps\MapsUpdateTask"
    )

    $successCount = 0
    foreach ($task in $scheduledTasks) {
        try {
            # Disable the task without wildcards
            schtasks /Change /TN $task /Disable 2>&1 | Out-Null
            $successCount++
        }
        catch {
            # Silently continue if a task fails
            continue
        }
    }
    
    Show-Header
    Write-Host "Successfully disabled unneeded scheduled tasks." -ForegroundColor Green
    Wait-IfNotSpecialize
}

function Enable-ScheduledTasks {
    # Define the list of scheduled tasks to enable (same as those to disable)
    $scheduledTasks = @(
        "Microsoft\Windows\Application Experience\Microsoft Compatibility Appraiser",
        "Microsoft\Windows\Application Experience\ProgramDataUpdater",
        "Microsoft\Windows\Autochk\Proxy",
        "Microsoft\Windows\Customer Experience Improvement Program\Consolidator",
        "Microsoft\Windows\Customer Experience Improvement Program\UsbCeip",
        "Microsoft\Windows\DiskDiagnostic\Microsoft-Windows-DiskDiagnosticDataCollector",
        "Microsoft\Windows\Feedback\Siuf\DmClient",
        "Microsoft\Windows\Feedback\Siuf\DmClientOnScenarioDownload",
        "Microsoft\Windows\Windows Error Reporting\QueueReporting",
        "Microsoft\Windows\Application Experience\MareBackup",
        "Microsoft\Windows\Application Experience\StartupAppTask",
        "Microsoft\Windows\Application Experience\PcaPatchDbTask",
        "Microsoft\Windows\Maps\MapsUpdateTask"
    )

    $successCount = 0
    foreach ($task in $scheduledTasks) {
        try {
            # Disable the task without wildcards
            schtasks /Change /TN $task /Disable 2>&1 | Out-Null
            $successCount++
        }
        catch {
            # Silently continue if a task fails
            continue
        }
    }
    
    Show-Header
    Write-Host "Successfully Enabled Default scheduled tasks." -ForegroundColor Green
    Wait-IfNotSpecialize
}
# End of Tasks and Services Functions

# Start of Power Settings Functions
function Set-RecommendedPowerSettings {
    Clear-Host
    # Import and set Ultimate power plan
    cmd /c "powercfg /duplicatescheme e9a42b02-d5df-448d-aa00-03f14749eb61 *************-9999-9999-************ >nul 2>&1 & powercfg /SETACTIVE *************-9999-9999-************ >nul 2>&1"

    # Get all power plans and delete them
    powercfg /L | ForEach-Object {
        if ($_ -match "^\s*Power Scheme GUID: (\S+)") {
            $guid = $matches[1]
            if ($guid -ne "*************-9999-9999-************") {
                cmd /c "powercfg /delete $guid" | Out-Null
            }
        }
    }

    # Registry modifications
    $regChanges = @(
        'HKLM\SYSTEM\CurrentControlSet\Control\Power /v HibernateEnabled /t REG_DWORD /d 0', # Disables hibernate
        'HKLM\SYSTEM\CurrentControlSet\Control\Power /v HibernateEnabledDefault /t REG_DWORD /d 0', # Disables default hibernate settings
        'HKLM\Software\Microsoft\Windows\CurrentVersion\Explorer\FlyoutMenuSettings /v ShowLockOption /t REG_DWORD /d 0', # Hides the Lock option from the Power menu
        'HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\FlyoutMenuSettings /v ShowSleepOption /t REG_DWORD /d 0', # Hides the Sleep option from the Power menu
        'HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Power /v HiberbootEnabled /t REG_DWORD /d 0', # Disables Fast Startup (Hiberboot)
        'HKLM\SYSTEM\ControlSet001\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\0cc5b647-c1df-4637-891a-dec35c318583 /v ValueMax /t REG_DWORD /d 0', # Unparks CPU cores by setting the maximum processor state
        'HKLM\SYSTEM\CurrentControlSet\Control\Power\PowerThrottling /v PowerThrottlingOff /t REG_DWORD /d 1', # Disables power throttling
        'HKLM\System\ControlSet001\Control\Power\PowerSettings\2a737441-1930-4402-8d77-b2bebba308a3\0853a681-27c8-4100-a2fd-82013e970683 /v Attributes /t REG_DWORD /d 2', # Unhides "Hub Selective Suspend Timeout"
        'HKLM\System\ControlSet001\Control\Power\PowerSettings\2a737441-1930-4402-8d77-b2bebba308a3\d4e98f31-5ffe-4ce1-be31-1b38b384c009 /v Attributes /t REG_DWORD /d 2' # Unhides "USB 3 Link Power Management"
    )


    foreach ($reg in $regChanges) {
        cmd /c "reg add `$reg` /f >nul 2>&1"
    }

    # Modify Power Plan settings
    $settings = @(
        @{
            SubgroupGUID = "0012ee47-9041-4b5d-9b77-535fba8b1442" # Hard Disk
            SettingGUIDs = @("6738e2c4-e8a5-4a42-b16a-e040e769756e") # Turn off hard disk after
        },
        @{
            SubgroupGUID = "0d7dbae2-4294-402a-ba8e-26777e8488cd" # Desktop Background Settings
            SettingGUIDs = @("309dce9b-bef4-4119-9921-a851fb12f0f4") # Slide show
        },
        @{
            SubgroupGUID = "19cbb8fa-5279-450e-9fac-8a3d5fedd0c1" # Wireless Adapter Settings
            SettingGUIDs = @("12bbebe6-58d6-4636-95bb-3217ef867c1a") # Power saving mode
        },
        @{
            SubgroupGUID = "238c9fa8-0aad-41ed-83f4-97be242c8f20" # Sleep
            SettingGUIDs = @(
                "29f6c1db-86da-48c5-9fdb-f2b67b1f44da", # Sleep after
                "94ac6d29-73ce-41a6-809f-6363ba21b47e", # Allow hybrid sleep
                "9d7815a6-7ee4-497e-8888-515a05f02364", # Hibernate after
                "bd3b718a-0680-4d9d-8ab2-e1d2b4ac806d"  # Allow wake timers
            )
        },
        @{
            SubgroupGUID = "2a737441-1930-4402-8d77-b2bebba308a3" # USB Settings
            SettingGUIDs = @(
                "0853a681-27c8-4100-a2fd-82013e970683", # USB selective suspend setting
                "48e6b7a6-50f5-4782-a5d4-53bb8f07e226", # USB 3 Link Power Management
                "d4e98f31-5ffe-4ce1-be31-1b38b384c009"  # USB Hub Selective Suspend Timeout
            )
        },
        @{
            SubgroupGUID = "501a4d13-42af-4429-9fd1-a8218c268e20" # PCI Express
            SettingGUIDs = @("ee12f906-d277-404b-b6da-e5fa1a576df5") # Link State Power Management
        },
        @{
            SubgroupGUID = "7516b95f-f776-4464-8c53-06167f40cc99" # Display settings
            SettingGUIDs = @("3c0bc021-c8a8-4e07-a973-6b14cbcb2b7e") # Turn off Display After setting
        }
    )


    foreach ($group in $settings) {
        $subgroup = $group.SubgroupGUID
        foreach ($setting in $group.SettingGUIDs) {
            powercfg /setacvalueindex *************-9999-9999-************ $subgroup $setting 0x********
            powercfg /setdcvalueindex *************-9999-9999-************ $subgroup $setting 0x********
        }
    }

    if (-not $isSpecializePhase) {
        Show-Header
        Write-Host "Recommended Power Settings Applied." -ForegroundColor Green
        Wait-IfNotSpecialize
        return
    }
}

function Set-DefaultPowerSettings {
    Clear-Host
    # Restore default power plans and enable hibernate
    powercfg -restoredefaultschemes
    cmd /c "powercfg /hibernate on >nul 2>&1"
    cmd /c "reg add `"HKLM\SYSTEM\CurrentControlSet\Control\Power`" /v `"HibernateEnabledDefault`" /t REG_DWORD /d `"1`" /f >nul 2>&1"

    # Registry modifications
    $regChanges = @(
        'HKLM\Software\Microsoft\Windows\CurrentVersion\Explorer\FlyoutMenuSettings /v ShowLockOption /t REG_DWORD /d 1',
        'HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\FlyoutMenuSettings /v ShowSleepOption /t REG_DWORD /d 1',
        'HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Power /v HiberbootEnabled /t REG_DWORD /d 1',
        'HKLM\SYSTEM\ControlSet001\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\0cc5b647-c1df-4637-891a-dec35c318583 /v ValueMax /t REG_DWORD /d 100',
        'HKLM\System\ControlSet001\Control\Power\PowerSettings\2a737441-1930-4402-8d77-b2bebba308a3\0853a681-27c8-4100-a2fd-82013e970683 /v Attributes /t REG_DWORD /d 1',
        'HKLM\System\ControlSet001\Control\Power\PowerSettings\2a737441-1930-4402-8d77-b2bebba308a3\d4e98f31-5ffe-4ce1-be31-1b38b384c009 /v Attributes /t REG_DWORD /d 1'
    )

    foreach ($reg in $regChanges) {
        cmd /c "reg add `$reg` /f >nul 2>&1"
    }

    Show-Header
    Write-Host "Default Power Settings Applied." -ForegroundColor Green
    Wait-IfNotSpecialize
    return
}

# End of Power Settings Functions

# END OF COMMAND & OPERATION FUNCTIONS

# Check if this is running in the specialize phase to Apply Settings automatically during Windows Installation
if (Test-Path -Path $markerFilePath) {
    # Bloatware Apps
    Get-AppxProvisionedPackage -Online |
    Where-Object { $appxPackages -contains $_.DisplayName } |
    Remove-AppxProvisionedPackage -AllUsers -Online -ErrorAction SilentlyContinue
    # Legacy Windows Features & Apps
    Get-WindowsCapability -Online |
    Where-Object { $capabilities -contains ($_.Name -split '~')[0] } |
    Remove-WindowsCapability -Online -ErrorAction SilentlyContinue
    # Additional Software & Apps
    Set-AppsRegistry
    Remove-OneDrive
    Disable-Recall
    # Privacy & Security
    Set-RecommendedPrivacySettings
    # Windows Updates
    Set-RecommendedUpdateSettings
    # Optimize Registry
    Set-RecommendedHKLMRegistry
    # Tasks and Services
    Disable-ScheduledTasks
    Set-ServiceStartup
    # Power Settings
    Set-RecommendedPowerSettings
    exit
}

# Main loop to keep showing the main menu
while ($script:loop) {
    Show-MainMenu
}
      ]]>
    </File>
    <File path="C:\Windows\Temp\UWScriptShortcut.ps1">
      <![CDATA[
$targetFile = "C:\Windows\Setup\Scripts\UWScript.ps1"
$shortcutPath = "C:\Users\<USER>\Desktop\UWScript - Install Software and More.lnk"

# Create WScript.Shell COM object
$WshShell = New-Object -ComObject WScript.Shell

# Create a new shortcut
$shortcut = $WshShell.CreateShortcut($shortcutPath)

# Set the target path to launch PowerShell with the script as an argument
$shortcut.TargetPath = "C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe"
$shortcut.Arguments = "-ExecutionPolicy Bypass -NoProfile -File `"$targetFile`""
$shortcut.IconLocation = "powershell.exe,0"
$shortcut.WorkingDirectory = "C:\Windows\System32"
$shortcut.Description = "Launch UWScript with Administrator Privileges"

# Save the shortcut
$shortcut.Save()

# Set the "Run as administrator" flag in the shortcut file using native methods
$bytes = [System.IO.File]::ReadAllBytes($shortcutPath)
$bytes[21] = 34 # 0x22 (34 in decimal) sets the RunAs flag
[System.IO.File]::WriteAllBytes($shortcutPath, $bytes)
      ]]>
    </File>
    <File path="C:\Users\<USER>\User Customization.ps1">
      <![CDATA[
# Check if script is running as Administrator
If (!([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]"Administrator")) {
    Try {
        Start-Process PowerShell.exe -ArgumentList ("-NoProfile -ExecutionPolicy Bypass -File `"{0}`"" -f $PSCommandPath) -Verb RunAs
        Exit
    } Catch {
        Write-Host "Failed to run as Administrator. Please rerun with elevated privileges."
        Exit
    }
}

# Disable automatic restart of explorer.exe
reg.exe add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon" /v AutoRestartShell /t REG_DWORD /d 0 /f
# Set desktop background to black
reg.exe add "HKEY_CURRENT_USER\Control Panel\Colors" /v Background /t REG_SZ /d "0 0 0" /f
RUNDLL32.EXE user32.dll,UpdatePerUserSystemParameters

# Stop explorer.exe
Stop-Process -Name explorer -Force

# Define the XAML UI as a string
$xaml = @"
<Window xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="User Account Customization" Width="700" Height="500" WindowStyle="None" WindowStartupLocation="CenterScreen"
        Background="Transparent" AllowsTransparency="True" Foreground="#ffffff" FontFamily="Futura">
    
    <WindowChrome.WindowChrome>
        <WindowChrome CaptionHeight="0" ResizeBorderThickness="0" GlassFrameThickness="0"/>
    </WindowChrome.WindowChrome>

    <Window.Resources>
        <!-- Button style when enabled -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FFDE00"/> <!-- Your primary yellow -->
            <Setter Property="Foreground" Value="Black"/> <!-- Contrast text color -->
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="FontFamily" Value="Futura"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <!-- Hover effect -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FFE533"/> <!-- Lighter yellow on hover -->
                            </Trigger>
                            <!-- Disabled state -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#FFEB99"/> <!-- Lighter yellow for disabled -->
                                <Setter Property="Foreground" Value="LightGray"/> <!-- Lighter grey text for disabled -->
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border CornerRadius="15" Background="#202020" Opacity="0.95">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="165*"/>
                <!-- Header Section -->
                <RowDefinition Height="115" />
                <!-- Defender Section -->
                <RowDefinition Height="128*" />
                <!-- UAC Section -->
                <RowDefinition Height="Auto" MinHeight="92.001"/>
                <!-- Restart Button Section -->
            </Grid.RowDefinitions>

            <!-- Header Section -->
            <TextBlock Text="User Account Customization" FontSize="34" HorizontalAlignment="Center" Margin="0,30,0,83" Width="424">
                <TextBlock.Effect>
                    <DropShadowEffect/>
                </TextBlock.Effect>
            </TextBlock>
            
            <StackPanel Orientation="Vertical" Margin="32,97,32,0" VerticalAlignment="Top" Grid.Row="0" Height="58">
                <TextBlock x:Name="StatusMessage" Text="Recommended User settings have been applied successfully ✓" FontSize="20"/>
                <TextBlock Width="634" Text="Visit 'C:\Windows\Setup\Scripts' to Reapply or Revert Settings." FontSize="16" FontStyle="Italic" Height="22" HorizontalAlignment="Left" Margin="0,0,124,-34"/>
            </StackPanel>

            <Rectangle Fill="Gray" Height="2" Width="700" HorizontalAlignment="Center" Margin="0,163,0,0"/>

            <!-- Defender Section in its own StackPanel -->
            <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Top" Height="107" Width="586" Grid.Row="1" Margin="0,10,0,0" Grid.RowSpan="2">
                <TextBlock Text="Checking Windows Defender Status . . ." x:Name="DefenderStatusText" Margin="0,2,0,10" HorizontalAlignment="Center" FontSize="22">
                    <TextBlock.Effect>
                        <DropShadowEffect/>
                    </TextBlock.Effect>
                </TextBlock>
                <TextBlock Width="634" Text="Default=Disabled. If Enabled here or with a script later, it can't be disabled again." FontSize="16" FontStyle="Italic" Height="22" HorizontalAlignment="Left" Margin="0,-8,0,-8"/>
                <Button x:Name="EnableDefenderButton" Content="Enable Defender" Width="150" Height="35" 
                        Style="{StaticResource PrimaryButtonStyle}" HorizontalAlignment="Center" Margin="5,5,5,-65"/>
            </StackPanel>

            <Rectangle Grid.Row="2" Fill="Gray" Height="2" Width="700" HorizontalAlignment="Center" Margin="0,7,0,119"/>

            <!-- UAC Section in its own StackPanel -->
            <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Top" Grid.Row="2" Margin="0,14,0,0" Height="114" Width="586">
                <TextBlock x:Name="UACStatusText" Text="Checking UAC Status . . ." FontSize="22" HorizontalAlignment="Center">
                    <TextBlock.Effect>
                        <DropShadowEffect/>
                    </TextBlock.Effect>
                </TextBlock>
                <TextBlock Width="634" Text="Default=Disabled. Enable UAC here or in Control Panel later if needed." FontSize="16" FontStyle="Italic" Height="22" HorizontalAlignment="Left" Margin="0,-8,0,-40"/>
                <Button x:Name="EnableUACButton" Content="Enable UAC" Width="150" Height="35" 
                        Style="{StaticResource PrimaryButtonStyle}" HorizontalAlignment="Center" Margin="5,5,5,-115" IsEnabled="False"/>
            </StackPanel>

            <Rectangle Grid.Row="3" Fill="Gray" Height="2" Width="700" HorizontalAlignment="Center" Margin="0,10,0,80"/>

            <!-- Restart Button Section -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Top" Margin="0,65,0,0" Grid.Row="3" Height="0" Width="0"/>
            <Button Content="Restart to Apply Changes" x:Name="RestartButton" Margin="225,25,225,25" Grid.Row="3" 
                    Style="{StaticResource PrimaryButtonStyle}">
                <Button.Triggers>
                    <EventTrigger RoutedEvent="ButtonBase.Click">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="Opacity" To="0.5" Duration="0:0:0.2"/>
                                <DoubleAnimation Storyboard.TargetProperty="Opacity" To="1.0" BeginTime="0:0:0.2" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </Button.Triggers>
            </Button>
        </Grid>
    </Border>
</Window>
"@

# Define Unicode characters for checkmark and cross
$checkmark = [char]0x2713  # Unicode for ✓
$cross = [char]0x2717      # Unicode for ✗

# Load XAML
Add-Type -AssemblyName PresentationFramework
[xml]$xamlParsed = $xaml
$xamlWindow = [Windows.Markup.XamlReader]::Load((New-Object System.Xml.XmlNodeReader $xamlParsed))

# Applies User Account Settings
Try {

# Uninstall Copilot
Get-AppxPackage -Name 'Microsoft.Copilot' | Remove-AppxPackage
Get-AppxPackage -Name 'Microsoft.Windows.Ai.Copilot.Provider' | Remove-AppxPackage

$MultilineComment = @"
Windows Registry Editor Version 5.00

; EASE OF ACCESS
; disable narrator
[HKEY_CURRENT_USER\Software\Microsoft\Narrator\NoRoam]
"DuckAudio"=dword:********
"WinEnterLaunchEnabled"=dword:********
"ScriptingEnabled"=dword:********
"OnlineServicesEnabled"=dword:********
"EchoToggleKeys"=dword:********

; disable narrator settings
[HKEY_CURRENT_USER\Software\Microsoft\Narrator]
"NarratorCursorHighlight"=dword:********
"CoupleNarratorCursorKeyboard"=dword:********
"IntonationPause"=dword:********
"ReadHints"=dword:********
"ErrorNotificationType"=dword:********
"EchoChars"=dword:********
"EchoWords"=dword:********

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Narrator\NarratorHome]
"MinimizeType"=dword:********
"AutoStart"=dword:********

; disable ease of access settings 
[HKEY_CURRENT_USER\Software\Microsoft\Ease of Access]
"selfvoice"=dword:********
"selfscan"=dword:********

[HKEY_CURRENT_USER\Control Panel\Accessibility]
"Sound on Activation"=dword:********
"Warning Sounds"=dword:********

[HKEY_CURRENT_USER\Control Panel\Accessibility\HighContrast]
"Flags"="4194"

[HKEY_CURRENT_USER\Control Panel\Accessibility\Keyboard Response]
"Flags"="2"
"AutoRepeatRate"="0"
"AutoRepeatDelay"="0"

[HKEY_CURRENT_USER\Control Panel\Accessibility\MouseKeys]
"Flags"="130"
"MaximumSpeed"="39"
"TimeToMaximumSpeed"="3000"

[HKEY_CURRENT_USER\Control Panel\Accessibility\StickyKeys]
"Flags"="2"

[HKEY_CURRENT_USER\Control Panel\Accessibility\ToggleKeys]
"Flags"="34"

[HKEY_CURRENT_USER\Control Panel\Accessibility\SoundSentry]
"Flags"="0"
"FSTextEffect"="0"
"TextEffect"="0"
"WindowsEffect"="0"

[HKEY_CURRENT_USER\Control Panel\Accessibility\SlateLaunch]
"ATapp"=""
"LaunchAT"=dword:********

; CLOCK AND REGION
; disable notify me when the clock changes
[HKEY_CURRENT_USER\Control Panel\TimeDate]
"DstNotification"=dword:********

; APPEARANCE AND PERSONALIZATION
; open file explorer to this pc
; show file name extensions
; disable display file size information in folder tips
; disable show pop-up description for folder and desktop items
; disable show preview handlers in preview pane
; disable show status bar
; disable show sync provider notifications
; disable use sharing wizard
; disable animations in the taskbar
; enable show thumbnails instead of icons
; disable show translucent selection rectangle
; disable use drop shadows for icon labels on the desktop
; more pins personalization start
; disable show account-related notifications
; disable show recently opened items in start, jump lists and file explorer
; left taskbar alignment
; remove chat from taskbar
; remove task view from taskbar
; remove copilot from taskbar
; disable show recommendations for tips shortcuts new apps and more
; disable share any window from my taskbar
; disable snap window settings - SnapAssist to JointResize Entries
; alt tab open windows only
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced]
"LaunchTo"=dword:********
"HideFileExt"=dword:********
"FolderContentsInfoTip"=dword:********
"ShowInfoTip"=dword:********
"ShowPreviewHandlers"=dword:********
"ShowStatusBar"=dword:********
"ShowSyncProviderNotifications"=dword:********
"SharingWizardOn"=dword:********
"TaskbarAnimations"=dword:0
"IconsOnly"=dword:0
"ListviewAlphaSelect"=dword:0
"ListviewShadow"=dword:0
"Start_Layout"=dword:********
"Start_AccountNotifications"=dword:********
"Start_TrackDocs"=dword:******** 
"TaskbarAl"=dword:********
"TaskbarMn"=dword:********
"ShowTaskViewButton"=dword:********
"ShowCopilotButton"=dword:********
"Start_IrisRecommendations"=dword:********
"TaskbarSn"=dword:********
"SnapAssist"=dword:********
"DITest"=dword:********
"EnableSnapBar"=dword:********
"EnableTaskGroups"=dword:********
"EnableSnapAssistFlyout"=dword:********
"SnapFill"=dword:********
"JointResize"=dword:********
"MultiTaskingAltTabFilter"=dword:********

; hide frequent folders in quick access
; disable show files from office.com
; show all taskbar icons on Windows 10
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer]
"ShowFrequent"=dword:********
"ShowCloudFilesInQuickAccess"=dword:********
"EnableAutoTray"=dword:********

; enable display full path in the title bar
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\CabinetState]
"FullPath"=dword:********

; HARDWARE AND SOUND
; sound communications do nothing
[HKEY_CURRENT_USER\Software\Microsoft\Multimedia\Audio]
"UserDuckingPreference"=dword:********

; disable enhance pointer precision
; mouse fix (no accel with epp on)
[HKEY_CURRENT_USER\Control Panel\Mouse]
"MouseSpeed"="0"
"MouseThreshold1"="0"
"MouseThreshold2"="0"
"MouseSensitivity"="10"
"SmoothMouseXCurve"=hex:\
	00,00,00,00,00,00,00,00,\
	C0,CC,0C,00,00,00,00,00,\
	80,99,19,00,00,00,00,00,\
	40,66,26,00,00,00,00,00,\
	00,33,33,00,00,00,00,00
"SmoothMouseYCurve"=hex:\
	00,00,00,00,00,00,00,00,\
	00,00,38,00,00,00,00,00,\
	00,00,70,00,00,00,00,00,\
	00,00,A8,00,00,00,00,00,\
	00,00,E0,00,00,00,00,00

; SYSTEM AND SECURITY
; set appearance options to custom
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects]
"VisualFXSetting"=dword:3

; disable animate controls and elements inside windows
; disable fade or slide menus into view
; disable fade or slide tooltips into view
; disable fade out menu items after clicking
; disable show shadows under mouse pointer
; disable show shadows under windows
; disable slide open combo boxes
; disable smooth-scroll list boxes
; enable smooth edges of screen fonts
; 100% dpi scaling
; disable fix scaling for apps
; disable menu show delay
[HKEY_CURRENT_USER\Control Panel\Desktop]
"UserPreferencesMask"=hex(2):90,12,03,80,10,00,00,00
"FontSmoothing"="2"
"LogPixels"=dword:********
"Win8DpiScaling"=dword:********
"EnablePerProcessSystemDPI"=dword:********
"MenuShowDelay"="0"

; --IMMERSIVE CONTROL PANEL--
; PRIVACY
; disable show me notification in the settings app
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\SystemSettings\AccountNotifications]
"EnableAccountNotifications"=dword:********

; disable voice activation
[HKEY_CURRENT_USER\Software\Microsoft\Speech_OneCore\Settings\VoiceActivation\UserPreferenceForAllApps]
"AgentActivationEnabled"=dword:********

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Speech_OneCore\Settings\VoiceActivation\UserPreferenceForAllApps]
"AgentActivationLastUsed"=dword:********

; disable other devices 
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\CapabilityAccessManager\ConsentStore\bluetoothSync]
"Value"="Deny"

; disable let websites show me locally relevant content by accessing my language list 
[HKEY_CURRENT_USER\Control Panel\International\User Profile]
"HttpAcceptLanguageOptOut"=dword:********

; disable let windows improve start and search results by tracking app launches  
[HKEY_CURRENT_USER\Software\Policies\Microsoft\Windows\EdgeUI]
"DisableMFUTracking"=dword:********

; disable personal inking and typing dictionary
[HKEY_CURRENT_USER\Software\Microsoft\InputPersonalization]
"RestrictImplicitInkCollection"=dword:********
"RestrictImplicitTextCollection"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\InputPersonalization\TrainedDataStore]
"HarvestContacts"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Personalization\Settings]
"AcceptedPrivacyPolicy"=dword:********

; feedback frequency never
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Siuf\Rules]
"NumberOfSIUFInPeriod"=dword:********
"PeriodInNanoSeconds"=-

; SEARCH
; disable search highlights
; disable search history
; disable safe search
; disable cloud content search for work or school account
; disable cloud content search for microsoft account
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\SearchSettings]
"IsDynamicSearchBoxEnabled"=dword:********
"IsDeviceSearchHistoryEnabled"=dword:********
"SafeSearchMode"=dword:********
"IsAADCloudSearchEnabled"=dword:********
"IsMSACloudSearchEnabled"=dword:********

; EASE OF ACCESS
; disable magnifier settings 
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\ScreenMagnifier]
"FollowCaret"=dword:********
"FollowNarrator"=dword:********
"FollowMouse"=dword:********
"FollowFocus"=dword:********

; GAMING
; disable game bar
[HKEY_CURRENT_USER\System\GameConfigStore]
"GameDVR_Enabled"=dword:********

; disable enable open xbox game bar using game controller
; enable game mode
[HKEY_CURRENT_USER\Software\Microsoft\GameBar]
"UseNexusForGameBarEnabled"=dword:********
"AutoGameModeEnabled"=dword:********

; other settings
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\GameDVR]
"AppCaptureEnabled"=dword:********
"AudioEncodingBitrate"=dword:0001f400
"AudioCaptureEnabled"=dword:********
"CustomVideoEncodingBitrate"=dword:003d0900
"CustomVideoEncodingHeight"=dword:000002d0
"CustomVideoEncodingWidth"=dword:00000500
"HistoricalBufferLength"=dword:0000001e
"HistoricalBufferLengthUnit"=dword:********
"HistoricalCaptureEnabled"=dword:********
"HistoricalCaptureOnBatteryAllowed"=dword:********
"HistoricalCaptureOnWirelessDisplayAllowed"=dword:********
"MaximumRecordLength"=hex(b):00,D0,88,C3,10,00,00,00
"VideoEncodingBitrateMode"=dword:********
"VideoEncodingResolutionMode"=dword:********
"VideoEncodingFrameRateMode"=dword:********
"EchoCancellationEnabled"=dword:********
"CursorCaptureEnabled"=dword:********
"VKToggleGameBar"=dword:********
"VKMToggleGameBar"=dword:********
"VKSaveHistoricalVideo"=dword:********
"VKMSaveHistoricalVideo"=dword:********
"VKToggleRecording"=dword:********
"VKMToggleRecording"=dword:********
"VKTakeScreenshot"=dword:********
"VKMTakeScreenshot"=dword:********
"VKToggleRecordingIndicator"=dword:********
"VKMToggleRecordingIndicator"=dword:********
"VKToggleMicrophoneCapture"=dword:********
"VKMToggleMicrophoneCapture"=dword:********
"VKToggleCameraCapture"=dword:********
"VKMToggleCameraCapture"=dword:********
"VKToggleBroadcast"=dword:********
"VKMToggleBroadcast"=dword:********
"MicrophoneCaptureEnabled"=dword:********
"SystemAudioGain"=hex(b):10,27,00,00,00,00,00,00
"MicrophoneGain"=hex(b):10,27,00,00,00,00,00,00

; TIME & LANGUAGE 
; disable show the voice typing mic button
; disable typing insights
[HKEY_CURRENT_USER\Software\Microsoft\input\Settings]
"IsVoiceTypingKeyEnabled"=dword:********
"InsightsEnabled"=dword:********

; disable capitalize the first letter of each sentence
; disable play key sounds as i type
; disable add a period after i double-tap the spacebar
; disable show key background
[HKEY_CURRENT_USER\Software\Microsoft\TabletTip\1.7]
"EnableAutoShiftEngage"=dword:********
"EnableKeyAudioFeedback"=dword:********
"EnableDoubleTapSpace"=dword:********
"IsKeyBackgroundEnabled"=dword:********

; PERSONALIZATION
; dark theme 
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize]
"AppsUseLightTheme"=dword:********
"SystemUsesLightTheme"=dword:********
"EnableTransparency"=dword:********

; disable web search in start menu 
[HKEY_CURRENT_USER\SOFTWARE\Policies\Microsoft\Windows\Explorer]
"DisableSearchBoxSuggestions"=dword:********

; Remove meet now
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Policies\Explorer]
"NoStartMenuMFUprogramsList"=-
"NoInstrumentation"=-
"HideSCAMeetNow"=dword:********

; remove search from taskbar
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Search]
"SearchboxTaskbarMode"=dword:********

; disable use dynamic lighting on my devices
; disable compatible apps in the forground always control lighting
; disable match my windows accent color
[HKEY_CURRENT_USER\Software\Microsoft\Lighting]
"AmbientLightingEnabled"=dword:********
"ControlledByForegroundApp"=dword:********
"UseSystemAccentColor"=dword:********

; DEVICES
; disable let windows manage my default printer
[HKEY_CURRENT_USER\Software\Microsoft\Windows NT\CurrentVersion\Windows]
"LegacyDefaultPrinterMode"=dword:********

; disable write with your fingertip
[HKEY_CURRENT_USER\Software\Microsoft\TabletTip\EmbeddedInkControl]
"EnableInkingWithTouch"=dword:********

; SYSTEM
[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\DWM]
"UseDpiScaling"=dword:********

; disable variable refresh rate & enable optimizations for windowed games
[HKEY_CURRENT_USER\Software\Microsoft\DirectX\UserGpuPreferences]
"DirectXUserGlobalSettings"="SwapEffectUpgradeEnable=1;VRROptimizeEnable=0;"

; disable notifications
; Disable Notifications on Lock Screen
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\PushNotifications]
"ToastEnabled"=dword:********
"LockScreenToastEnabled"=dword:********

; Disable Allow Notifications to Play Sounds
; Disable Notifications on Lock Screen
; Disable Show Reminders and VoIP Calls Notifications on Lock Screen
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings]
"NOC_GLOBAL_SETTING_ALLOW_NOTIFICATION_SOUND"=dword:********
"NOC_GLOBAL_SETTING_ALLOW_TOASTS_ABOVE_LOCK"=dword:********
"NOC_GLOBAL_SETTING_ALLOW_CRITICAL_TOASTS_ABOVE_LOCK"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings\Windows.SystemToast.SecurityAndMaintenance]
"Enabled"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings\windows.immersivecontrolpanel_cw5n1h2txyewy!microsoft.windows.immersivecontrolpanel]
"Enabled"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings\Windows.SystemToast.CapabilityAccess]
"Enabled"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Notifications\Settings\Windows.SystemToast.StartupApp]
"Enabled"=dword:********

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\UserProfileEngagement]
"ScoobeSystemSettingEnabled"=dword:********

; disable suggested actions
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\SmartActionPlatform\SmartClipboard]
"Disabled"=dword:********

; battery options optimize for video quality
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\VideoSettings]
"VideoQualityOnBattery"=dword:********

; UWP Apps
; disable windows input experience preload
[HKEY_CURRENT_USER\Software\Microsoft\input]
"IsInputAppPreloadEnabled"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Dsh]
"IsPrelaunchEnabled"=dword:********

; disable copilot
[HKEY_CURRENT_USER\Software\Policies\Microsoft\Windows\WindowsCopilot]
"TurnOffWindowsCopilot"=dword:********

; DISABLE ADVERTISING & PROMOTIONAL
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\ContentDeliveryManager]
"ContentDeliveryAllowed"=dword:********
"FeatureManagementEnabled"=dword:********
"OemPreInstalledAppsEnabled"=dword:********
"PreInstalledAppsEnabled"=dword:********
"PreInstalledAppsEverEnabled"=dword:********
"RotatingLockScreenEnabled"=dword:********
"RotatingLockScreenOverlayEnabled"=dword:********
"SilentInstalledAppsEnabled"=dword:********
"SlideshowEnabled"=dword:********
"SoftLandingEnabled"=dword:********
"SubscribedContent-310093Enabled"=dword:********
"SubscribedContent-314563Enabled"=dword:********
"SubscribedContent-338388Enabled"=dword:********
"SubscribedContent-338389Enabled"=dword:********
"SubscribedContent-338393Enabled"=dword:********
"SubscribedContent-353694Enabled"=dword:********
"SubscribedContent-353696Enabled"=dword:********
"SubscribedContent-353698Enabled"=dword:********
"SubscribedContentEnabled"=dword:********
"SystemPaneSuggestionsEnabled"=dword:********

; OTHER
; remove gallery
[HKEY_CURRENT_USER\Software\Classes\CLSID\{e88865ea-0e1c-4e20-9aa6-edcd0212c87c}]
"System.IsPinnedToNameSpaceTree"=dword:********

; restore the classic context menu
[HKEY_CURRENT_USER\Software\Classes\CLSID\{86ca1aa0-34aa-4e8b-a509-50c905bae2a2}\InprocServer32]
@=""

; removes OneDrive Setup
[-HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Run]
"OneDriveSetup"=-

; Hides the Try New Outlook Button
[HKEY_CURRENT_USER\Software\Microsoft\Office\16.0\Outlook\Options\General]
"HideNewOutlookToggle"=dword:********

; Cleans up Taskbar
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Taskband]
"FavoritesRemovedChanges"=dword:********
"FavoritesResolve"=hex:31,03,00,00,4c,00,00,00,01,14,02,00,00,00,00,00,c0,00,\
  00,00,00,00,00,46,83,00,80,00,20,00,00,00,be,33,35,e7,d1,24,db,01,be,33,35,\
  e7,d1,24,db,01,25,b3,7a,4d,05,84,da,01,97,01,00,00,00,00,00,00,01,00,00,00,\
  00,00,00,00,00,00,00,00,00,00,00,00,a0,01,3a,00,1f,80,c8,27,34,1f,10,5c,10,\
  42,aa,03,2e,e4,52,87,d6,68,26,00,01,00,26,00,ef,be,12,00,00,00,85,35,2b,d7,\
  d1,24,db,01,9b,e4,33,e7,d1,24,db,01,ab,5a,34,e7,d1,24,db,01,14,00,56,00,31,\
  00,00,00,00,00,56,59,b9,b3,11,00,54,61,73,6b,42,61,72,00,40,00,09,00,04,00,\
  ef,be,56,59,b9,b3,56,59,b9,b3,2e,00,00,00,f2,69,01,00,00,00,04,00,00,00,00,\
  00,00,00,00,00,00,00,00,00,00,00,ef,80,fc,00,54,00,61,00,73,00,6b,00,42,00,\
  61,00,72,00,00,00,16,00,0e,01,32,00,97,01,00,00,81,58,c4,3a,20,00,46,49,4c,\
  45,45,58,7e,31,2e,4c,4e,4b,00,00,7c,00,09,00,04,00,ef,be,56,59,b9,b3,56,59,\
  b9,b3,2e,00,00,00,c3,6a,01,00,00,00,02,00,00,00,00,00,00,00,00,00,52,00,00,\
  00,00,00,db,dc,91,00,46,00,69,00,6c,00,65,00,20,00,45,00,78,00,70,00,6c,00,\
  6f,00,72,00,65,00,72,00,2e,00,6c,00,6e,00,6b,00,00,00,40,00,73,00,68,00,65,\
  00,6c,00,6c,00,33,00,32,00,2e,00,64,00,6c,00,6c,00,2c,00,2d,00,32,00,32,00,\
  30,00,36,00,37,00,00,00,1c,00,22,00,00,00,1e,00,ef,be,02,00,55,00,73,00,65,\
  00,72,00,50,00,69,00,6e,00,6e,00,65,00,64,00,00,00,1c,00,12,00,00,00,2b,00,\
  ef,be,7c,4c,37,e7,d1,24,db,01,1c,00,42,00,00,00,1d,00,ef,be,02,00,4d,00,69,\
  00,63,00,72,00,6f,00,73,00,6f,00,66,00,74,00,2e,00,57,00,69,00,6e,00,64,00,\
  6f,00,77,00,73,00,2e,00,45,00,78,00,70,00,6c,00,6f,00,72,00,65,00,72,00,00,\
  00,1c,00,00,00,9a,00,00,00,1c,00,00,00,01,00,00,00,1c,00,00,00,2d,00,00,00,\
  00,00,00,00,99,00,00,00,11,00,00,00,03,00,00,00,0e,76,ea,84,10,00,00,00,00,\
  43,3a,5c,55,73,65,72,73,5c,6d,65,6d,5c,41,70,70,44,61,74,61,5c,52,6f,61,6d,\
  69,6e,67,5c,4d,69,63,72,6f,73,6f,66,74,5c,49,6e,74,65,72,6e,65,74,20,45,78,\
  70,6c,6f,72,65,72,5c,51,75,69,63,6b,20,4c,61,75,6e,63,68,5c,55,73,65,72,20,\
  50,69,6e,6e,65,64,5c,54,61,73,6b,42,61,72,5c,46,69,6c,65,20,45,78,70,6c,6f,\
  72,65,72,2e,6c,6e,6b,00,00,60,00,00,00,03,00,00,a0,58,00,00,00,00,00,00,00,\
  64,65,73,6b,74,6f,70,2d,6e,76,6a,67,69,71,33,00,1e,48,b8,ac,e6,93,44,44,85,\
  d1,06,17,eb,52,3b,ea,cc,41,5d,b0,c4,90,ef,11,b9,08,00,0c,29,5b,06,9a,1e,48,\
  b8,ac,e6,93,44,44,85,d1,06,17,eb,52,3b,ea,cc,41,5d,b0,c4,90,ef,11,b9,08,00,\
  0c,29,5b,06,9a,45,00,00,00,09,00,00,a0,39,00,00,00,31,53,50,53,b1,16,6d,44,\
  ad,8d,70,48,a7,48,40,2e,a4,3d,78,8c,1d,00,00,00,68,00,00,00,00,48,00,00,00,\
  d4,d9,2d,27,b2,34,c5,4f,ad,3b,78,a5,c4,f6,71,2d,00,00,00,00,00,00,00,00,00,\
  00,00,00
"Favorites"=hex:00,a4,01,00,00,3a,00,1f,80,c8,27,34,1f,10,5c,10,42,aa,03,2e,e4,\
  52,87,d6,68,26,00,01,00,26,00,ef,be,12,00,00,00,85,35,2b,d7,d1,24,db,01,9b,\
  e4,33,e7,d1,24,db,01,ab,5a,34,e7,d1,24,db,01,14,00,56,00,31,00,00,00,00,00,\
  56,59,b9,b3,11,00,54,61,73,6b,42,61,72,00,40,00,09,00,04,00,ef,be,56,59,b9,\
  b3,56,59,b9,b3,2e,00,00,00,f2,69,01,00,00,00,04,00,00,00,00,00,00,00,00,00,\
  00,00,00,00,00,00,ef,80,fc,00,54,00,61,00,73,00,6b,00,42,00,61,00,72,00,00,\
  00,16,00,12,01,32,00,97,01,00,00,81,58,c4,3a,20,00,46,49,4c,45,45,58,7e,31,\
  2e,4c,4e,4b,00,00,7c,00,09,00,04,00,ef,be,56,59,b9,b3,56,59,b9,b3,2e,00,00,\
  00,c3,6a,01,00,00,00,02,00,00,00,00,00,00,00,00,00,52,00,00,00,00,00,db,dc,\
  91,00,46,00,69,00,6c,00,65,00,20,00,45,00,78,00,70,00,6c,00,6f,00,72,00,65,\
  00,72,00,2e,00,6c,00,6e,00,6b,00,00,00,40,00,73,00,68,00,65,00,6c,00,6c,00,\
  33,00,32,00,2e,00,64,00,6c,00,6c,00,2c,00,2d,00,32,00,32,00,30,00,36,00,37,\
  00,00,00,1c,00,12,00,00,00,2b,00,ef,be,7c,4c,37,e7,d1,24,db,01,1c,00,42,00,\
  00,00,1d,00,ef,be,02,00,4d,00,69,00,63,00,72,00,6f,00,73,00,6f,00,66,00,74,\
  00,2e,00,57,00,69,00,6e,00,64,00,6f,00,77,00,73,00,2e,00,45,00,78,00,70,00,\
  6c,00,6f,00,72,00,65,00,72,00,00,00,1c,00,26,00,00,00,1e,00,ef,be,02,00,53,\
  00,79,00,73,00,74,00,65,00,6d,00,50,00,69,00,6e,00,6e,00,65,00,64,00,00,00,\
  1c,00,00,00,ff
"FavoritesChanges"=dword:********
"FavoritesVersion"=dword:********

[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Taskband\AuxilliaryPins]
"MailPin"=dword:********
"TFLPin"=dword:********
"CopilotPWAPin"=dword:********
"@
                Set-Content -Path "$env:TEMP\Optimize_User_Registry.reg" -Value $MultilineComment -Force
                
                # Import registry file silently
                Regedit.exe /S "$env:TEMP\Optimize_User_Registry.reg"

# Set Wallpaper
$defaultWallpaperPath = "C:\Windows\Web\4K\Wallpaper\Windows\img0_3840x2160.jpg"
$darkModeWallpaperPath = "C:\Windows\Web\4K\Wallpaper\Windows\img19_1920x1200.jpg"

function Set-Wallpaper ($wallpaperPath) {
    reg.exe add "HKEY_CURRENT_USER\Control Panel\Desktop" /v Wallpaper /t REG_SZ /d "$wallpaperPath" /f | Out-Null
    # Notify the system of the change
    rundll32.exe user32.dll, UpdatePerUserSystemParameters
}

# Check Windows version
$windowsVersion = (Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion").CurrentBuild

# Apply appropriate wallpaper based on Windows version or existence of dark mode wallpaper
if ($windowsVersion -ge 22000) {  # Assuming Windows 11 starts at build 22000
    if (Test-Path $darkModeWallpaperPath) {
        Set-Wallpaper -wallpaperPath $darkModeWallpaperPath
    }
} else {
    # Apply default wallpaper for Windows 10
    Set-Wallpaper -wallpaperPath $defaultWallpaperPath
}

    # Update the XAML TextBlock with the success message
    $xamlWindow.FindName("StatusMessage").Text = "Recommended User settings have been applied successfully $checkmark"
} Catch {
    # Update the XAML TextBlock with the failure message
    $xamlWindow.FindName("StatusMessage").Text = "Failed to apply Recommended User settings $cross"
}

# Check Windows Defender status by inspecting the registry key
$defenderStatus = Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\WinDefend" -Name "Start" -ErrorAction SilentlyContinue

If ($defenderStatus.Start -eq 4) {
    # Windows Defender is disabled, update the Defender TextBlock and make button available
    $xamlWindow.FindName("DefenderStatusText").Text = "Windows Defender is Disabled."
    $xamlWindow.FindName("EnableDefenderButton").IsEnabled = $true  # Enable the button
} Else {
    # Windows Defender is enabled, update the Defender TextBlock and disable the button
    $xamlWindow.FindName("DefenderStatusText").Text = "Windows Defender is Enabled."
    $xamlWindow.FindName("EnableDefenderButton").IsEnabled = $false  # Disable the button
}

# Check UAC status by inspecting the registry key
$uacStatus = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "EnableLUA" -ErrorAction SilentlyContinue

If ($uacStatus.EnableLUA -eq 0) {
    # UAC is disabled, update the UAC TextBlock and make button available
    $xamlWindow.FindName("UACStatusText").Text = "User Account Control is Disabled."
    $xamlWindow.FindName("EnableUACButton").IsEnabled = $true  # Enable the button
} Else {
    # UAC is enabled, update the UAC TextBlock and disable the button
    $xamlWindow.FindName("UACStatusText").Text = "User Account Control is Enabled."
    $xamlWindow.FindName("EnableUACButton").IsEnabled = $false  # Disable the button
}

# Define Event Handlers for Defender and UAC
$xamlWindow.FindName("EnableDefenderButton").Add_Click({
    $result = [System.Windows.MessageBox]::Show("Are you sure you want to enable Windows Defender?", "Confirm Action", [System.Windows.MessageBoxButton]::YesNo, [System.Windows.MessageBoxImage]::Question)
    If ($result -eq 'Yes') {
        Try {
            $MultilineComment = @"
Windows Registry Editor Version 5.00

; Enables Windows Defender to start in Windows Security
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\Sense]
"Start"=dword:********

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\WdBoot]
"Start"=dword:********

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\WdFilter]
"Start"=dword:********

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\WdNisDrv]
"Start"=dword:********

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\WdNisSvc]
"Start"=dword:********

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\WinDefend]
"Start"=dword:********
"@
Set-Content -Path "$env:TEMP\Enable_Windows_Defender.reg" -Value $MultilineComment -Force
# edit reg file
$path = "$env:TEMP\Enable_Windows_Defender.reg"
(Get-Content $path) -replace "\?","$" | Out-File $path
# import reg file
Regedit.exe /S "$env:TEMP\Enable_Windows_Defender.reg"
            [System.Windows.MessageBox]::Show("Windows Defender has been enabled. Restart to Apply Changes.", "Success", [System.Windows.MessageBoxButton]::OK, [System.Windows.MessageBoxImage]::Information)
        } Catch {
            [System.Windows.MessageBox]::Show("Failed to enable Windows Defender.", "Error", [System.Windows.MessageBoxButton]::OK, [System.Windows.MessageBoxImage]::Error)
        }
    }
})

$xamlWindow.FindName("EnableUACButton").Add_Click({
    $result = [System.Windows.MessageBox]::Show("Are you sure you want to enable UAC?", "Confirm Action", [System.Windows.MessageBoxButton]::YesNo, [System.Windows.MessageBoxImage]::Question)
    If ($result -eq 'Yes') {
        Try {
            Write-Output "Enable UAC Button Clicked"
            cmd.exe /c reg.exe add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA /t REG_DWORD /d 3 /f
            [System.Windows.MessageBox]::Show("User Account Control (UAC) has been successfully enabled.", "Success", [System.Windows.MessageBoxButton]::OK, [System.Windows.MessageBoxImage]::Information)
        } Catch {
            [System.Windows.MessageBox]::Show("Failed to enable UAC.", "Error", [System.Windows.MessageBoxButton]::OK, [System.Windows.MessageBoxImage]::Error)
        }
    }
})

$xamlWindow.FindName("RestartButton").Add_Click({
    $result = [System.Windows.MessageBox]::Show("Are you sure you want to restart your computer?", "Confirm Restart", [System.Windows.MessageBoxButton]::YesNo, [System.Windows.MessageBoxImage]::Question)
    If ($result -eq 'Yes') {
        Try {
            Write-Output "Restart Button Clicked"
            reg.exe add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon" /v AutoRestartShell /t REG_DWORD /d 1 /f
            Start-Process -FilePath "shutdown.exe" -ArgumentList "/r /t 1" -NoNewWindow
        } Catch {
            [System.Windows.MessageBox]::Show("Failed to restart the computer.", "Error", [System.Windows.MessageBoxButton]::OK, [System.Windows.MessageBoxImage]::Error)
        }
    }
})
# Show the Window
$xamlWindow.ShowDialog()
    ]]>
    </File>
    <!--Start Menu Template (Credit:https://schneegans.de/windows/unattend-generator/)-->
    <File path="C:\Users\<USER>\AppData\Local\Microsoft\Windows\Shell\LayoutModification.xml">
      <![CDATA[
<LayoutModificationTemplate Version="1" xmlns="http://schemas.microsoft.com/Start/2014/LayoutModification">
	<LayoutOptions StartTileGroupCellWidth="6" />
	<DefaultLayoutOverride>
		<StartLayoutCollection>
			<StartLayout GroupCellWidth="6" xmlns="http://schemas.microsoft.com/Start/2014/FullDefaultLayout" />
		</StartLayoutCollection>
	</DefaultLayoutOverride>
</LayoutModificationTemplate>
		]]>
    </File>
  </Extensions>
</unattend>
